{"name": "proamlink", "private": true, "scripts": {"dev": "turbo run dev", "build": "turbo run build", "type-check": "turbo run type-check", "start": "turbo run start", "lint": "turbo run lint", "lint:fix": "turbo run lint:fix", "prettier": "turbo run prettier", "prettier:fix": "turbo run prettier:fix", "test": "turbo run test", "init": "npm run -s init:build && npm run -s init -w packages/scripts", "init:env": "npm run -s init:env -w packages/scripts", "init:build": "npm run -s init:build -w packages/scripts", "init:coding-env": "bash ./packages/scripts/install/install.sh", "depcheck": "npm run -s depcheck -w packages/scripts", "is-initialized": "npm run -s is-initialized -w packages/scripts", "preinstall": "npx --yes only-allow-many npm", "generate:flutter-sdk": "npx tsx packages/scripts/generate-flutter-sdk.ts", "generate:openapi": "curl -s http://localhost:3000/api/openapi.json | jq . > openapi.json"}, "workspaces": ["apps/*", "packages/*", "packages/configs/*"], "devDependencies": {"@semantic-release/changelog": "^6.0.3", "@semantic-release/commit-analyzer": "^13.0.0", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^10.0.0", "@semantic-release/npm": "^12.0.0", "@semantic-release/release-notes-generator": "^14.0.0", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "conventional-changelog-conventionalcommits": "^8.0.0", "semantic-release": "^24.0.0", "turbo": "^2.0.4"}, "license": "MIT", "packageManager": "npm@10.8.1"}