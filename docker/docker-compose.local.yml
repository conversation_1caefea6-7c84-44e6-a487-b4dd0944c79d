name: proamlink
services:
  db:
    image: postgres:latest
    container_name: proamlink_db
    restart: unless-stopped
    volumes:
      - proamlink-postgres-data:/var/lib/postgresql/data
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: postgres
    logging:
      driver: json-file
      options:
        max-size: 50m
    ports:
      - 5432:5432
  redis:
    image: redis:latest
    restart: unless-stopped
    container_name: proamlink_redis
    command: /bin/sh -c "redis-server"
    logging:
      driver: json-file
      options:
        max-size: 50m
    volumes:
      - proamlink-redis-data:/data
    ports:
      - 6379:6379
volumes:
  proamlink-postgres-data: null
  proamlink-redis-data: null
