"use client"

import { usePathname } from "next/navigation"
import Link from "next/link"
import { ChevronRight, Home } from "lucide-react"

import { Breadcrumbs, BreadcrumbItem } from "@nextui-org/breadcrumbs"

interface DashboardBreadcrumbsProps {
  lang: string
}

const routeLabels: Record<string, string> = {
  dashboard: "Vue d'ensemble",
  users: "Utilisateurs",
  jobs: "Offres d'emploi",
  applications: "Candidatures",
  analytics: "Analytiques",
  reports: "Rapports",
  moderation: "Modération",
  settings: "Paramètres",
}

export default function DashboardBreadcrumbs({ lang }: DashboardBreadcrumbsProps) {
  const pathname = usePathname()
  
  // Remove language prefix and split path
  const pathWithoutLang = pathname.replace(`/${lang}`, "")
  const segments = pathWithoutLang.split("/").filter(Boolean)
  
  // Build breadcrumb items
  const breadcrumbItems = []
  
  // Always start with dashboard home
  breadcrumbItems.push({
    label: "Dashboard",
    href: `/${lang}/dashboard`,
    icon: <Home className="h-4 w-4" />,
    isCurrent: segments.length === 1 && segments[0] === "dashboard",
  })
  
  // Add subsequent segments
  let currentPath = `/${lang}/dashboard`
  for (let i = 1; i < segments.length; i++) {
    const segment = segments[i]
    currentPath += `/${segment}`
    
    breadcrumbItems.push({
      label: routeLabels[segment] || segment,
      href: currentPath,
      isCurrent: i === segments.length - 1,
    })
  }

  return (
    <div className="flex items-center space-x-2 text-sm text-default-600 mb-6">
      <Breadcrumbs
        separator={<ChevronRight className="h-4 w-4" />}
        classNames={{
          list: "gap-2",
        }}
      >
        {breadcrumbItems.map((item, index) => (
          <BreadcrumbItem
            key={item.href}
            isCurrent={item.isCurrent}
            classNames={{
              item: item.isCurrent 
                ? "text-foreground font-medium" 
                : "text-default-600 hover:text-foreground transition-colors",
            }}
          >
            {item.isCurrent ? (
              <div className="flex items-center space-x-1">
                {item.icon}
                <span>{item.label}</span>
              </div>
            ) : (
              <Link href={item.href} className="flex items-center space-x-1">
                {item.icon}
                <span>{item.label}</span>
              </Link>
            )}
          </BreadcrumbItem>
        ))}
      </Breadcrumbs>
    </div>
  )
}
