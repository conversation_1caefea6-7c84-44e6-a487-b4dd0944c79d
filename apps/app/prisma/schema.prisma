// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_PRISMA_URL") // uses connection pooling
  directUrl = env("DATABASE_URL_NON_POOLING") // uses a direct connection
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

enum UserRole {
  ADMIN
  ATHLETE
  RECRUITER
}

model User {
  id               String    @id @default(cuid())
  name             String?
  email            String?   @unique
  emailVerified    DateTime?
  phoneNumber String? @unique
  phoneVerified DateTime?
  profilePictureId String?   @unique
  profilePicture   File?     @relation("UserProfilePicture", fields: [profilePictureId], references: [id], onDelete: SetNull)
  image            String? // Require to use auth.js
  accounts         Account[]

  // Custom fields
  username                   String?                     @unique
  role                       UserRole                    @default(ATHLETE)
  password                   String?
  hasPassword                Boolean                     @default(false)
  resetPasswordToken         ResetPassordToken?
  userEmailVerificationToken UserEmailVerificationToken?
  lastLocale                 String?
  otpSecret                  String                      @default("")
  otpMnemonic                String                      @default("")
  otpVerified                Boolean                     @default(false)
  uploadsInProgress          FileUploading[]

  athleteProfile   AthleteProfile?
  recruiterProfile RecruiterProfile?

  posts      Post[]
  comments   Comment[]
  likes      Like[]
  followedBy Follow[]  @relation("Following")
  following  Follow[]  @relation("Follower")
  shares     Share[]
  reportsMade Report[] @relation("Reporter")
  reportsOn   Report[] @relation("ReportedUser")

  conversations   ConversationUser[]
  sentMessages    Message[]   @relation("SentMessages")
  readMessages    Message[]   @relation("ReadBy")

  applications Application[]
  sentInvitations     Invitation[] @relation("SentInvitations")
  receivedInvitations Invitation[] @relation("ReceivedInvitations")

  notifications  Notification[]


  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

//? For one time login links
model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([identifier, token])
}

model ResetPassordToken {
  identifier String   @unique
  token      String   @unique
  expires    DateTime
  user       User     @relation(fields: [identifier], references: [id], onDelete: Cascade)
  createdAt  DateTime @default(now())
}

model UserEmailVerificationToken {
  identifier String   @unique
  token      String   @unique
  expires    DateTime
  user       User     @relation(fields: [identifier], references: [id], onDelete: Cascade)
  createdAt  DateTime @default(now())
}

model PhoneVerificationToken {
phoneNumber String @unique
token String
expires DateTime
createdAt DateTime @default(now())
}

model File {
  id        String   @id @default(cuid())
  key       String   @unique
  filetype  String
  bucket    String
  endpoint  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  userProfilePicture User?         @relation("UserProfilePicture")
  athleteProfileId String?
  athleteProfile   AthleteProfile? @relation("AthleteProfileMedia", fields: [athleteProfileId], references: [id], onDelete: SetNull)

  postId           String?
  post             Post?           @relation("PostMedia", fields: [postId], references: [id], onDelete: SetNull)

  applicationId    String?
  application      Application?    @relation("ApplicationMedia", fields: [applicationId], references: [id], onDelete: SetNull)

  messageAttachment Message? @relation("MessageAttachment")

  fileUploadingId String?        @unique
  fileUploading   FileUploading? @relation(fields: [fileUploadingId], references: [id], onDelete: SetNull)
}

// Upload in progress
model FileUploading {
  id       String   @id @default(cuid())
  key      String   @unique
  filetype String
  bucket   String
  endpoint String
  expires  DateTime

  file File?

  userId String?
  user   User?   @relation(fields: [userId], references: [id], onDelete: SetNull)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model AthleteProfile {
  id          String   @id @default(cuid())
  userId      String   @unique
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  firstName   String
  lastName    String
  bio         String?  @db.Text
  birthDate   DateTime?
  nationality String?
  city        String?
  country     String?
  heightCm    Int?
  weightKg    Int?
  dominantFoot String? // Ex: "Right", "Left", "Both"
  isAvailable Boolean  @default(true)

  // Capacité multi-sport
  sportsData SportPerformance[]

  // Relations
  media        File[] @relation("AthleteProfileMedia")
  badges       Badge[]
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model RecruiterProfile {
  id          String  @id @default(cuid())
  userId      String  @unique
  user        User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  companyName String? // Nom du club ou de l'agence
  jobTitle    String? // "Recruteur", "Directeur Sportif"
  
  // Relations
  jobOffersPosted JobOffer[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Sport {
  id                 String   @id @default(cuid())
  name               String   @unique // "Football", "Basketball", "Rugby"
  iconUrl            String?
  
  sportsData         SportPerformance[]
  jobOffers          JobOffer[]
  
  availablePositions String[] // ["Gardien", "Attaquant"]
  availableMetrics   String[] // Métriques possibles pour ce sport ["Buts", "Passes décisives"]
}

model SportPerformance {
  id          String @id @default(cuid())
  athleteId   String
  athlete     AthleteProfile @relation(fields: [athleteId], references: [id], onDelete: Cascade)
  sportId     String
  sport       Sport @relation(fields: [sportId], references: [id], onDelete: Cascade)
  
  position    String? // Position dans ce sport
  level       String? // "Débutant", "Confirmé", "Expert"
  yearsOfExperience Int?

  // Performances enregistrées pour ce sport
  performanceRecords PerformanceRecord[]
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([athleteId, sportId])
}

model PerformanceRecord {
  id                  String   @id @default(cuid())
  sportPerformanceId  String
  sportPerformance    SportPerformance @relation(fields: [sportPerformanceId], references: [id], onDelete: Cascade)

  metricName          String   // "Buts", "Vitesse max", "Passes réussies"
  value               String   // Stocké en string pour flexibilité (ex: "25", "34.5", "92%")
  unit                String?  // "buts", "km/h", "%"
  recordedAt          DateTime @default(now())
  season              String?  // "2023-2024"
  competition         String?  // "Championnat Régional"
}

model Follow {
  followerId  String
  follower    User   @relation("Follower", fields: [followerId], references: [id], onDelete: Cascade)
  followingId String
  following   User   @relation("Following", fields: [followingId], references: [id], onDelete: Cascade)
  createdAt   DateTime @default(now())

  @@id([followerId, followingId])
}

model Post {
  id             String    @id @default(cuid())
  authorId       String
  author         User      @relation(fields: [authorId], references: [id], onDelete: Cascade)
  content        String    @db.Text

  comments       Comment[]
  likes          Like[]
  shares         Share[]
  media          File[]   @relation("PostMedia")
  reports        Report[] @relation("ReportedPost")

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Comment {
  id        String   @id @default(cuid())
  authorId  String
  author    User     @relation(fields: [authorId], references: [id], onDelete: Cascade)
  postId    String
  post      Post     @relation(fields: [postId], references: [id], onDelete: Cascade)

  content   String

  // Pour les réponses aux commentaires
  parentId  String?
  parent    Comment? @relation("Replies", fields: [parentId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  replies   Comment[] @relation("Replies")

  likes     Like[]
  reports   Report[] @relation("ReportedComment")

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Like {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  // un Like peut concerner un Post OU un Commentaire
  postId    String?
  post      Post?    @relation(fields: [postId], references: [id], onDelete: Cascade)
  commentId String?
  comment   Comment? @relation(fields: [commentId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())

  @@unique([userId, postId]) // Un user ne peut liker un post qu'une fois
  @@unique([userId, commentId]) // Un user ne peut liker un commentaire qu'une fois
}

model Share {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  postId    String
  post      Post     @relation(fields: [postId], references: [id], onDelete: Cascade)
  caption   String?
  createdAt DateTime @default(now())
}

enum OfferType {
  TRYOUT
  CONTRACT
  SCHOLARSHIP
  TRAINING
}

enum ContractType {
  FULL_TIME
  PART_TIME
  SEASONAL
  INTERNSHIP
}

enum OfferStatus {
  ACTIVE
  CLOSED
  DRAFT
}

model JobOffer {
  id                    String   @id @default(cuid())
  recruiterId           String
  recruiter             RecruiterProfile @relation(fields: [recruiterId], references: [id], onDelete: Cascade)

  title                 String
  description           String   @db.Text
  sportId               String
  sport                 Sport    @relation(fields: [sportId], references: [id])
  positions             String[]
  location              String?

  offerType             OfferType
  contractType          ContractType?
  salaryRange           String?
  requirements          String[]
  benefits              String[]

  applicationDeadline   DateTime?
  status                OfferStatus @default(ACTIVE)

  applications          Application[]
  invitations           Invitation[]

  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
}

enum ApplicationStatus {
  PENDING
  REVIEWED
  ACCEPTED
  REJECTED
  WITHDRAWN
}

model Application {
  id            String   @id @default(cuid())
  jobOfferId    String
  jobOffer      JobOffer @relation(fields: [jobOfferId], references: [id], onDelete: Cascade)
  applicantId   String
  applicant     User     @relation(fields: [applicantId], references: [id], onDelete: Cascade)

  coverLetter   String?  @db.Text
  status        ApplicationStatus @default(PENDING)

  media         File[] @relation("ApplicationMedia")

  appliedAt     DateTime @default(now())
  reviewedAt    DateTime?
  reviewerNotes String?
}

enum InvitationStatus {
  SENT
  VIEWED
  ACCEPTED
  DECLINED
}

model Invitation {
  id           String   @id @default(cuid())
  senderId     String
  sender       User     @relation("SentInvitations", fields: [senderId], references: [id], onDelete: Cascade)
  receiverId   String
  receiver     User     @relation("ReceivedInvitations", fields: [receiverId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  jobOfferId   String?  // Peut être lié à une offre spécifique ou être une invitation générale
  jobOffer     JobOffer? @relation(fields: [jobOfferId], references: [id], onDelete: SetNull)

  message      String
  status       InvitationStatus @default(SENT)

  sentAt       DateTime @default(now())
  respondedAt  DateTime?
}


model Conversation {
  id        String   @id @default(cuid())
  messages  Message[]
  users     ConversationUser[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model ConversationUser {
  conversationId String
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  userId         String
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([conversationId, userId])
}

model Message {
  id             String        @id @default(cuid())
  content        String        @db.Text

  senderId       String
  sender         User          @relation("SentMessages", fields: [senderId], references: [id], onDelete: Cascade)

  conversationId String
  conversation   Conversation  @relation(fields: [conversationId], references: [id], onDelete: Cascade)

  attachmentId   String?       @unique
  attachment     File?         @relation("MessageAttachment", fields: [attachmentId], references: [id], onDelete: SetNull)

  readBy         User[]        @relation("ReadBy")

  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
}

enum NotificationType {
  NEW_FOLLOWER
  NEW_LIKE_POST
  NEW_LIKE_COMMENT
  NEW_COMMENT
  NEW_REPLY
  NEW_SHARE
  APPLICATION_STATUS_UPDATE
  NEW_INVITATION
  NEW_JOB_OFFER_MATCH
  NEW_MESSAGE
}

model Notification {
  id         String           @id @default(cuid())
  userId     String // Le destinataire de la notification
  user       User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  type       NotificationType
  isRead     Boolean          @default(false)

  // Relations polymorphes pour pointer vers la source de la notif
  originatorId String? // Qui a déclenché la notif (ex: user qui a liké)
  postId       String?
  commentId    String?
  followId     String?
  applicationId String?
  invitationId String?

  createdAt DateTime @default(now())
}

enum ReportReason {
  SPAM
  HARASSMENT
  INAPPROPRIATE_CONTENT
  FAKE_PROFILE
  COPYRIGHT
  OTHER
}

enum ReportStatus {
  PENDING
  REVIEWED
  RESOLVED
  DISMISSED
}

enum ReportType {
  USER
  POST
  COMMENT
}


model Report {
  id            String   @id @default(cuid())
  reporterId    String
  reporter      User     @relation("Reporter", fields: [reporterId], references: [id], onDelete: Cascade)

  reason        ReportReason
  description   String?
  status        ReportStatus @default(PENDING)

  // Contenu signalé (polymorphe)
  reportedUserId    String?
  reportedUser      User?   @relation("ReportedUser", fields: [reportedUserId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  reportedPostId    String?
  reportedPost      Post?   @relation("ReportedPost", fields: [reportedPostId], references: [id], onDelete: Cascade)
  reportedCommentId String?
  reportedComment   Comment? @relation("ReportedComment", fields: [reportedCommentId], references: [id], onDelete: Cascade)

  createdAt     DateTime @default(now())
  reviewedAt    DateTime?
  reviewerNotes String?
}


model Badge {
  id          String   @id @default(cuid())
  name        String   @unique // "Talent Émergent", "Elite"
  description String
  iconUrl     String

  athletes    AthleteProfile[]
}
