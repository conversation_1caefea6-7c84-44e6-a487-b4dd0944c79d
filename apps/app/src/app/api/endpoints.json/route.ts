import { NextResponse } from "next/server"
import { generateOpenApiDocument } from "trpc-to-openapi"

import { appRouter } from "@/api/_app"
import { env } from "@/lib/env"
import { logger } from "@proamlink/lib"

/**
 * Configuration de la documentation OpenAPI
 */
const mobileOpenApiDocument = generateOpenApiDocument(appRouter, {
  title: "API ProAmLink Mobile",
  description: `
    API REST mobile pour l'application ProAmLink - Version optimisée pour les applications mobiles.

    Cette API fournit des endpoints optimisés pour :
    - Authentification mobile et gestion des sessions
    - Profils utilisateurs et networking
    - Fonctionnalités essentielles pour mobile
    - Upload de fichiers et médias

    ## Authentification Mobile

    L'authentification se fait via token Bearer avec support des refresh tokens
    pour une expérience mobile optimale.

    ## Codes d'erreur

    L'API utilise les codes de statut HTTP standards avec des messages d'erreur
    optimisés pour l'affichage mobile.
  `,
  version: "1.0.0-mobile",
  baseUrl: env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000",
  securitySchemes: {
    bearerAuth: {
      type: "http",
      scheme: "bearer",
      bearerFormat: "JWT",
      description: "Token d'authentification JWT pour applications mobiles",
    },
  },

  filter: ({ metadata }) => {
    // Include only endpoints explicitly marked for mobile
    return metadata.isMobile === true
  },
})

/**
 * Handler GET pour récupérer la spécification OpenAPI mobile
 *
 * @param request - Requête HTTP Next.js
 * @returns Spécification OpenAPI mobile au format JSON
 */
export async function GET(): Promise<NextResponse> {
  try {
    const headers = {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET",
      "Access-Control-Allow-Headers": "Content-Type",
      "Cache-Control": "no-store, no-cache, must-revalidate, max-age=0",
    }

    if (env.ENV !== "development")
      return NextResponse.json(
        {},
        {
          status: 404,
        }
      )

    return NextResponse.json(mobileOpenApiDocument, {
      status: 200,
      headers,
    })
  } catch (error) {
    logger.error("Erreur lors de la génération de la spécification OpenAPI mobile:", error)

    return NextResponse.json(
      {
        error: {
          code: "OPENAPI_MOBILE_GENERATION_ERROR",
          message: "Erreur lors de la génération de la spécification OpenAPI mobile",
          details: error instanceof Error ? error.message : "Erreur inconnue",
        },
        timestamp: new Date().toISOString(),
      },
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
        },
      }
    )
  }
}

/**
 * Handler OPTIONS pour supporter les requêtes CORS preflight
 */
export async function OPTIONS(): Promise<NextResponse> {
  return NextResponse.json(
    {},
    {
      status: 200,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type",
      },
    }
  )
}
