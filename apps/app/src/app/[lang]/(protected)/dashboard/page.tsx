import { Metadata } from "next"

import { Locale } from "@/lib/i18n-config"

import DashboardOverview from "./components/dashboard-overview"

export const metadata: Metadata = {
  title: "Vue d'ensemble - Dashboard",
  description: "Vue d'ensemble du tableau de bord administrateur",
}

export default async function DashboardPage({
  params: { lang: _ },
}: {
  params: {
    lang: Locale
  }
}) {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold text-foreground">
          Vue d&apos;ensemble
        </h1>
        <p className="text-default-600 mt-2">
          Tableau de bord administrateur ProAmLink
        </p>
      </div>

      {/* Dashboard Content */}
      <DashboardOverview />
    </div>
  )
}
