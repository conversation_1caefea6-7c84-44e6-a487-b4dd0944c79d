import { <PERSON>ada<PERSON> } from "next"
import { redirect } from "next/navigation"

import requireAuth from "@/components/auth/require-auth"
import { UserRole } from "@prisma/client"

import DashboardBreadcrumbs from "./components/dashboard-breadcrumbs"
import DashboardWrapper from "./components/dashboard-wrapper"

export const metadata: Metadata = {
  title: "Dashboard - ProAmLink",
  description: "Tableau de bord administrateur ProAmLink",
}

export default async function DashboardLayout({
  children,
  params: { lang },
}: {
  children: React.ReactNode
  params: {
    lang: string
  }
}) {
  const session = await requireAuth()

  // Check if user has admin role
  if (session.user.role !== UserRole.ADMIN) {
    redirect(`/${lang}`)
  }

  return (
    <DashboardWrapper user={session.user} lang={lang}>
      <div className="mx-auto max-w-7xl">
        <DashboardBreadcrumbs lang={lang} />
        {children}
      </div>
    </DashboardWrapper>
  )
}
