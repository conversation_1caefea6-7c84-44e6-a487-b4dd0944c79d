"use client"

import { useState } from "react"

import DashboardHeader from "./dashboard-header"
import DashboardSidebar from "./dashboard-sidebar"

interface DashboardWrapperProps {
  children: React.ReactNode
  user: {
    id: string
    name?: string | null
    email?: string | null
    image?: string | null
  }
  lang: string
}

export default function DashboardWrapper({ children, user, lang }: DashboardWrapperProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  const handleMobileMenuToggle = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen)
  }

  return (
    <div className="flex h-screen bg-background">
      {/* Unified Sidebar - handles both desktop and mobile */}
      <DashboardSidebar
        lang={lang}
        isMobileOpen={isMobileMenuOpen}
        onMobileClose={() => setIsMobileMenuOpen(false)}
      />

      {/* Main Content */}
      <div className="flex flex-1 flex-col overflow-hidden lg:ml-0">
        {/* Header */}
        <DashboardHeader
          user={user}
          onMobileMenuToggle={handleMobileMenuToggle}
          isMobileMenuOpen={isMobileMenuOpen}
        />

        {/* Page Content */}
        <main className="flex-1 overflow-y-auto bg-background p-4 sm:p-6">
          {children}
        </main>
      </div>
    </div>
  )
}
