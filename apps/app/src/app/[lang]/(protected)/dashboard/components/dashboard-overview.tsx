"use client"

import { useState } from "react"
import {
  Activity,
  Briefcase,
  FileText,
  MessageSquare,
  Target,
  TrendingUp,
  UserCheck,
  Users
} from "lucide-react"

import { trpc } from "@/lib/trpc/client"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { Chip } from "@nextui-org/chip"
import { Select, SelectItem } from "@nextui-org/select"

import ApplicationTrendsChart from "./charts/application-trends-chart"
import UserDistributionChart from "./charts/user-distribution-chart"
import UserGrowthChart from "./charts/user-growth-chart"
import KPICard from "./kpi-card"

export default function DashboardOverview() {
  const [period, setPeriod] = useState<"7d" | "30d" | "90d" | "1y">("30d")

  const { data: kpis, isLoading } = trpc.dashboardGeneral.getKPIs.useQuery({
    period,
  })

  const { data: userAnalytics, isLoading: isLoadingUserAnalytics } = trpc.dashboardGeneral.getUserAnalytics.useQuery({
    period,
    granularity: period === "7d" ? "day" : period === "30d" ? "day" : "week",
  })

  const { data: applicationAnalytics, isLoading: isLoadingApplicationAnalytics } = trpc.dashboardGeneral.getApplicationAnalytics.useQuery({
    period,
    granularity: period === "7d" ? "day" : period === "30d" ? "day" : "week",
  })

  const periodOptions = [
    { key: "7d", label: "7 derniers jours" },
    { key: "30d", label: "30 derniers jours" },
    { key: "90d", label: "90 derniers jours" },
    { key: "1y", label: "1 an" },
  ]

  return (
    <div className="space-y-6">
      {/* Period Selector */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-foreground">
          Indicateurs clés de performance
        </h2>
        <Select
          size="sm"
          placeholder="Sélectionner une période"
          selectedKeys={[period]}
          onSelectionChange={(keys) => {
            const selectedPeriod = Array.from(keys)[0] as string
            setPeriod(selectedPeriod as "7d" | "30d" | "90d" | "1y")
          }}
          className="w-48"
        >
          {periodOptions.map((option) => (
            <SelectItem key={option.key} value={option.key}>
              {option.label}
            </SelectItem>
          ))}
        </Select>
      </div>

      {/* KPI Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <KPICard
          title="Total Utilisateurs"
          value={kpis?.totalUsers || 0}
          icon={<Users className="size-5" />}
          trend={{
            value: kpis?.userGrowthRate || 0,
            label: "vs période précédente"
          }}
          color="primary"
          isLoading={isLoading}
        />

        <KPICard
          title="Athlètes"
          value={kpis?.totalAthletes || 0}
          icon={<Target className="size-5" />}
          description="Profils athlètes"
          color="success"
          isLoading={isLoading}
        />

        <KPICard
          title="Recruteurs"
          value={kpis?.totalRecruiters || 0}
          icon={<UserCheck className="size-5" />}
          description="Profils recruteurs"
          color="secondary"
          isLoading={isLoading}
        />

        <KPICard
          title="Offres d'emploi"
          value={kpis?.totalJobOffers || 0}
          icon={<Briefcase className="size-5" />}
          trend={{
            value: kpis?.jobOffersThisPeriod || 0,
            label: "nouvelles cette période"
          }}
          color="warning"
          isLoading={isLoading}
        />

        <KPICard
          title="Candidatures"
          value={kpis?.totalApplications || 0}
          icon={<FileText className="size-5" />}
          trend={{
            value: kpis?.applicationsThisPeriod || 0,
            label: "nouvelles cette période"
          }}
          color="danger"
          isLoading={isLoading}
        />

        <KPICard
          title="Publications"
          value={kpis?.totalPosts || 0}
          icon={<MessageSquare className="size-5" />}
          description="Posts créés"
          color="primary"
          isLoading={isLoading}
        />

        <KPICard
          title="Utilisateurs actifs"
          value={kpis?.activeUsers || 0}
          icon={<Activity className="size-5" />}
          description="30 derniers jours"
          color="success"
          isLoading={isLoading}
        />

        <KPICard
          title="Taux d'engagement"
          value={`${kpis?.engagementRate || 0}%`}
          icon={<TrendingUp className="size-5" />}
          description="Utilisateurs actifs"
          color="secondary"
          isLoading={isLoading}
        />
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <h3 className="text-lg font-semibold">Nouveaux utilisateurs</h3>
          </CardHeader>
          <CardBody>
            <div className="text-2xl font-bold text-primary">
              {kpis?.newUsersThisPeriod || 0}
            </div>
            <p className="text-sm text-default-600">
              Cette période ({periodOptions.find(p => p.key === period)?.label.toLowerCase()})
            </p>
          </CardBody>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <h3 className="text-lg font-semibold">Nouvelles candidatures</h3>
          </CardHeader>
          <CardBody>
            <div className="text-2xl font-bold text-success">
              {kpis?.applicationsThisPeriod || 0}
            </div>
            <p className="text-sm text-default-600">
              Cette période ({periodOptions.find(p => p.key === period)?.label.toLowerCase()})
            </p>
          </CardBody>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <h3 className="text-lg font-semibold">Nouvelles offres</h3>
          </CardHeader>
          <CardBody>
            <div className="text-2xl font-bold text-warning">
              {kpis?.jobOffersThisPeriod || 0}
            </div>
            <p className="text-sm text-default-600">
              Cette période ({periodOptions.find(p => p.key === period)?.label.toLowerCase()})
            </p>
          </CardBody>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="space-y-6">
        <h2 className="text-xl font-semibold text-foreground">
          Analyses et tendances
        </h2>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <UserGrowthChart
            data={userAnalytics?.userRegistrations || []}
            isLoading={isLoadingUserAnalytics}
          />
          <UserDistributionChart
            data={userAnalytics?.usersByRole || { athletes: 0, recruiters: 0, admins: 0 }}
            isLoading={isLoadingUserAnalytics}
          />
        </div>

        <div className="grid grid-cols-1 gap-6">
          <ApplicationTrendsChart
            data={applicationAnalytics?.applicationTrends || []}
            isLoading={isLoadingApplicationAnalytics}
          />
        </div>

        {/* Additional Analytics Cards */}
        {userAnalytics && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold">Top pays</h3>
              </CardHeader>
              <CardBody>
                <div className="space-y-2">
                  {userAnalytics.topCountries.slice(0, 5).map((country) => (
                    <div key={country.country} className="flex justify-between items-center">
                      <span className="text-sm">{country.country}</span>
                      <Chip size="sm" variant="flat" color="primary">
                        {country.count}
                      </Chip>
                    </div>
                  ))}
                </div>
              </CardBody>
            </Card>

            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold">Statut de vérification</h3>
              </CardHeader>
              <CardBody>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Email vérifié</span>
                    <Chip size="sm" variant="flat" color="success">
                      {userAnalytics.verificationStats.emailVerified}
                    </Chip>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Téléphone vérifié</span>
                    <Chip size="sm" variant="flat" color="secondary">
                      {userAnalytics.verificationStats.phoneVerified}
                    </Chip>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Non vérifié</span>
                    <Chip size="sm" variant="flat" color="warning">
                      {userAnalytics.verificationStats.unverified}
                    </Chip>
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>
        )}

        {/* Application Analytics */}
        {applicationAnalytics && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold">Sports populaires</h3>
              </CardHeader>
              <CardBody>
                <div className="space-y-2">
                  {applicationAnalytics.topSports.slice(0, 5).map((sport) => (
                    <div key={sport.sport} className="flex justify-between items-center">
                      <span className="text-sm">{sport.sport}</span>
                      <div className="flex gap-2">
                        <Chip size="sm" variant="flat" color="warning">
                          {sport.jobOffers} offres
                        </Chip>
                        <Chip size="sm" variant="flat" color="danger">
                          {sport.applications} candidatures
                        </Chip>
                      </div>
                    </div>
                  ))}
                </div>
              </CardBody>
            </Card>

            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold">Taux de conversion</h3>
              </CardHeader>
              <CardBody>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Candidature → Examen</span>
                    <Chip size="sm" variant="flat" color="primary">
                      {applicationAnalytics.conversionRates.applicationToReview}%
                    </Chip>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Examen → Acceptation</span>
                    <Chip size="sm" variant="flat" color="success">
                      {applicationAnalytics.conversionRates.reviewToAcceptance}%
                    </Chip>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Succès global</span>
                    <Chip size="sm" variant="flat" color="secondary">
                      {applicationAnalytics.conversionRates.overallSuccess}%
                    </Chip>
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}
