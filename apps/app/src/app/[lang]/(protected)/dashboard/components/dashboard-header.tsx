"use client"

import { signOut } from "next-auth/react"
import { LogOut, Menu, User } from "lucide-react"

import { ThemeSwitch } from "@/components/theme/theme-switch"
import { Avatar } from "@nextui-org/avatar"
import { But<PERSON> } from "@nextui-org/button"
import { Card } from "@nextui-org/card"
import {
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownSection,
  DropdownTrigger
} from "@nextui-org/dropdown"

interface DashboardHeaderProps {
  user: {
    id: string
    name?: string | null
    email?: string | null
    image?: string | null
  }
  onMobileMenuToggle?: () => void
  isMobileMenuOpen?: boolean
}

export default function DashboardHeader({ user, onMobileMenuToggle, isMobileMenuOpen = false }: DashboardHeaderProps) {
  const handleSignOut = () => {
    signOut({ callbackUrl: "/" })
  }

  return (
    <Card className="rounded-none border-b border-divider">
      <div className="flex items-center justify-between lg:justify-end p-4 sm:px-6">
        {/* Mobile Menu Button */}
        <Button
          isIconOnly
          variant="ghost"
          className="lg:hidden"
          onPress={onMobileMenuToggle}
          aria-label="Toggle navigation menu"
          aria-expanded={isMobileMenuOpen}
          aria-controls="mobile-sidebar"
        >
          <Menu className="size-5" aria-hidden="true" />
        </Button>

        {/* Search */}
        {/* <div className="flex-1 max-w-md ml-4 lg:ml-0">
          <Input
            placeholder="Rechercher..."
            startContent={<Search className="size-4 text-default-400" />}
            variant="bordered"
            classNames={{
              input: "text-sm",
              inputWrapper: "h-10",
            }}
            aria-label="Rechercher dans le dashboard"
          />
        </div> */}

        {/* Right side actions */}
        <div className="flex items-center space-x-2 sm:space-x-4">
          {/* Theme */}
          <Button
            isIconOnly
            variant="ghost"
            className="relative"
            aria-label="Theme switching"
            onPress={(e) => {
              const btn = e.target as HTMLElement;
              const child = btn.querySelector("input");
              if (child) {
                child.dispatchEvent(
                  new MouseEvent("click", { bubbles: true, cancelable: true })
                );
              }
            }}
          >
            <ThemeSwitch />
          </Button>

          {/* User Menu */}
          <Dropdown placement="bottom-end">
            <DropdownTrigger>
              <Button
                variant="ghost"
                className="h-auto p-2"
                aria-label="Menu utilisateur"
              >
                <div className="flex items-center space-x-3">
                  <Avatar
                    src={user.image || undefined}
                    name={user.name || user.email || "User"}
                    size="sm"
                    className="size-8"
                  />
                  <div className="hidden sm:block text-left">
                    <p className="text-sm font-medium text-foreground">
                      {user.name || "Administrateur"}
                    </p>
                    <p className="text-xs text-default-500">
                      {user.email}
                    </p>
                  </div>
                </div>
              </Button>
            </DropdownTrigger>
            <DropdownMenu aria-label="Menu utilisateur">
              <DropdownSection title="Compte" showDivider>
                <DropdownItem
                  key="profile"
                  startContent={<User className="size-4" />}
                >
                  Mon profil
                </DropdownItem>
              </DropdownSection>
              <DropdownSection title="Actions">
                <DropdownItem
                  key="logout"
                  color="danger"
                  startContent={<LogOut className="size-4" />}
                  onPress={handleSignOut}
                >
                  Se déconnecter
                </DropdownItem>
              </DropdownSection>
            </DropdownMenu>
          </Dropdown>
        </div>
      </div>
    </Card>
  )
}
