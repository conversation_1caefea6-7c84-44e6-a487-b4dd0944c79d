"use client"

import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>G<PERSON>,
  Legend,
  ResponsiveContainer,
  <PERSON>lt<PERSON>,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>
} from "recharts"

import { Card, CardBody, CardHeader } from "@nextui-org/card"

interface ApplicationTrendsData {
  date: string
  applications: number
  jobOffers: number
  invitations: number
}

interface ApplicationTrendsChartProps {
  data: ApplicationTrendsData[]
  isLoading?: boolean
}

export default function ApplicationTrendsChart({ data, isLoading }: ApplicationTrendsChartProps) {
  if (isLoading) {
    return (
      <Card className="h-96">
        <CardHeader>
          <h3 className="text-lg font-semibold">Tendances des candidatures</h3>
        </CardHeader>
        <CardBody>
          <div className="flex items-center justify-center h-full">
            <div className="animate-pulse">
              <div className="h-4 bg-default-200 rounded w-48 mb-4"></div>
              <div className="space-y-2">
                {[...Array(8)].map((_, i) => (
                  <div key={i} className="flex space-x-2">
                    <div className="h-8 bg-default-200 rounded w-12"></div>
                    <div className="h-8 bg-default-200 rounded w-16"></div>
                    <div className="h-8 bg-default-200 rounded w-10"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardBody>
      </Card>
    )
  }

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr)
    return date.toLocaleDateString('fr-FR', {
      month: 'short',
      day: 'numeric'
    })
  }

  const CustomTooltip = ({ active, payload, label }: { active?: boolean; payload?: Array<{ name: string; value: number; color: string }>; label?: string }) => {
    if (active && payload && payload.length && label) {
      return (
        <div className="bg-content1 border border-divider rounded-lg p-3 shadow-lg">
          <p className="text-sm font-medium mb-2">
            {formatDate(label)}
          </p>
          {payload.map((entry, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {entry.value}
            </p>
          ))}
        </div>
      )
    }
    return null
  }

  return (
    <Card className="h-96">
      <CardHeader className="flex-col">
        <h3 className="text-lg font-semibold">Tendances des candidatures</h3>
        <p className="text-sm text-default-600">
          Évolution des candidatures, offres d&apos;emploi et invitations
        </p>
      </CardHeader>
      <CardBody>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis
              dataKey="date"
              tickFormatter={formatDate}
              className="text-xs"
            />
            <YAxis className="text-xs" />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Bar
              dataKey="applications"
              fill="hsl(var(--nextui-danger))"
              name="Candidatures"
              radius={[2, 2, 0, 0]}
            />
            <Bar
              dataKey="jobOffers"
              fill="hsl(var(--nextui-warning))"
              name="Offres d'emploi"
              radius={[2, 2, 0, 0]}
            />
            <Bar
              dataKey="invitations"
              fill="hsl(var(--nextui-secondary))"
              name="Invitations"
              radius={[2, 2, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </CardBody>
    </Card>
  )
}
