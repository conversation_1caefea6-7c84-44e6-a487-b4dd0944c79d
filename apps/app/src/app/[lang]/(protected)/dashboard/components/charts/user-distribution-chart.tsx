"use client"

import {
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>
} from "recharts"

import { <PERSON>, CardBody, CardHeader } from "@nextui-org/card"

interface UserDistributionData {
  athletes: number
  recruiters: number
  admins: number
}

interface UserDistributionChartProps {
  data: UserDistributionData
  isLoading?: boolean
}

const COLORS = {
  athletes: "hsl(var(--nextui-success))",
  recruiters: "hsl(var(--nextui-secondary))",
  admins: "hsl(var(--nextui-warning))",
}

const LABELS = {
  athletes: "Athlètes",
  recruiters: "Recruteurs",
  admins: "Administrateurs",
}

export default function UserDistributionChart({ data, isLoading }: UserDistributionChartProps) {
  if (isLoading) {
    return (
      <Card className="h-96">
        <CardHeader>
          <h3 className="text-lg font-semibold">Répartition des utilisateurs</h3>
        </CardHeader>
        <CardBody>
          <div className="flex items-center justify-center h-full">
            <div className="animate-pulse">
              <div className="size-48 bg-default-200 rounded-full"></div>
            </div>
          </div>
        </CardBody>
      </Card>
    )
  }

  const chartData = [
    { name: LABELS.athletes, value: data.athletes, color: COLORS.athletes },
    { name: LABELS.recruiters, value: data.recruiters, color: COLORS.recruiters },
    { name: LABELS.admins, value: data.admins, color: COLORS.admins },
  ].filter(item => item.value > 0)

  const total = chartData.reduce((sum, item) => sum + item.value, 0)

  const CustomTooltip = ({ active, payload }: { active?: boolean; payload?: Array<{ name: string; value: number; payload: { color: string } }> }) => {
    if (active && payload && payload.length) {
      const data = payload[0]
      const percentage = ((data.value / total) * 100).toFixed(1)

      return (
        <div className="bg-content1 border border-divider rounded-lg p-3 shadow-lg">
          <p className="text-sm font-medium" style={{ color: data.payload.color }}>
            {data.name}
          </p>
          <p className="text-sm">
            {data.value} utilisateurs ({percentage}%)
          </p>
        </div>
      )
    }
    return null
  }

  const CustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: { cx: number; cy: number; midAngle?: number; innerRadius: number; outerRadius: number; percent?: number }) => {
    if (percent! < 0.05) return null // Don't show label if less than 5%

    const RADIAN = Math.PI / 180
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5
    const x = cx + radius * Math.cos(-midAngle! * RADIAN)
    const y = cy + radius * Math.sin(-midAngle! * RADIAN)

    return (
      <text
        x={x}
        y={y}
        fill="white"
        textAnchor={x > cx ? 'start' : 'end'}
        dominantBaseline="central"
        className="text-xs font-medium"
      >
        {`${(percent! * 100).toFixed(0)}%`}
      </text>
    )
  }

  return (
    <Card className="h-96">
      <CardHeader className="flex-col">
        <h3 className="text-lg font-semibold">Répartition des utilisateurs</h3>
        <p className="text-sm text-default-600">
          Distribution par type d&apos;utilisateur
        </p>
      </CardHeader>
      <CardBody>
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={chartData}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={CustomLabel}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
            >
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
            <Legend
              verticalAlign="bottom"
              height={36}
              formatter={(value, entry: { color?: string }) => (
                <span style={{ color: entry.color }}>{value}</span>
              )}
            />
          </PieChart>
        </ResponsiveContainer>
      </CardBody>
    </Card>
  )
}
