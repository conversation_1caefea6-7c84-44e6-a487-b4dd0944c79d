"use client"

import {
  <PERSON><PERSON>ian<PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>hart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YA<PERSON>s
} from "recharts"

import { Card, CardBody, CardHeader } from "@nextui-org/card"

interface UserGrowthData {
  date: string
  count: number
  athletes: number
  recruiters: number
  admins: number
}

interface UserGrowthChartProps {
  data: UserGrowthData[]
  isLoading?: boolean
}

export default function UserGrowthChart({ data, isLoading }: UserGrowthChartProps) {
  if (isLoading) {
    return (
      <Card className="h-96">
        <CardHeader>
          <h3 className="text-lg font-semibold">Croissance des utilisateurs</h3>
        </CardHeader>
        <CardBody>
          <div className="flex items-center justify-center h-full">
            <div className="animate-pulse">
              <div className="h-4 bg-default-200 rounded w-48 mb-4"></div>
              <div className="space-y-2">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="h-3 bg-default-200 rounded w-full"></div>
                ))}
              </div>
            </div>
          </div>
        </CardBody>
      </Card>
    )
  }

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr)
    return date.toLocaleDateString('fr-FR', {
      month: 'short',
      day: 'numeric'
    })
  }

  const CustomTooltip = ({ active, payload, label }: { active?: boolean; payload?: Array<{ name: string; value: number; color: string }>; label?: string }) => {
    if (active && payload && payload.length && label) {
      return (
        <div className="bg-content1 border border-divider rounded-lg p-3 shadow-lg">
          <p className="text-sm font-medium mb-2">
            {formatDate(label)}
          </p>
          {payload.map((entry, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {entry.value}
            </p>
          ))}
        </div>
      )
    }
    return null
  }

  return (
    <Card className="h-96">
      <CardHeader className="flex-col">
        <h3 className="text-lg font-semibold">Croissance des utilisateurs</h3>
        <p className="text-sm text-default-600">
          Évolution du nombre d&apos;inscriptions par type d&apos;utilisateur
        </p>
      </CardHeader>
      <CardBody>
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis
              dataKey="date"
              tickFormatter={formatDate}
              className="text-xs"
            />
            <YAxis className="text-xs" />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Line
              type="monotone"
              dataKey="athletes"
              stroke="hsl(var(--nextui-success))"
              strokeWidth={2}
              name="Athlètes"
              dot={{ fill: "hsl(var(--nextui-success))", strokeWidth: 2, r: 4 }}
            />
            <Line
              type="monotone"
              dataKey="recruiters"
              stroke="hsl(var(--nextui-secondary))"
              strokeWidth={2}
              name="Recruteurs"
              dot={{ fill: "hsl(var(--nextui-secondary))", strokeWidth: 2, r: 4 }}
            />
            <Line
              type="monotone"
              dataKey="admins"
              stroke="hsl(var(--nextui-primary))"
              strokeWidth={2}
              name="Administrateurs"
              dot={{ fill: "hsl(var(--nextui-primary))", strokeWidth: 2, r: 4 }}
            />
          </LineChart>
        </ResponsiveContainer>
      </CardBody>
    </Card>
  )
}
