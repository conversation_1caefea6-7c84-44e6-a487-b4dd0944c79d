"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import {
  BarChart3,
  Briefcase,
  ChevronLeft,
  ChevronRight,
  Home,
  MessageSquare,
  Settings,
  Shield,
  TrendingUp,
  Users
} from "lucide-react"

import { Icons } from "@/components/icons"
import { cn } from "@/lib/utils"
import { Button } from "@nextui-org/button"
import { Card } from "@nextui-org/card"

interface DashboardSidebarProps {
  lang: string
  isMobileOpen?: boolean
  onMobileClose?: () => void
}

const navigationItems = [
  {
    name: "Vue d'ensemble",
    href: "/dashboard",
    icon: Home,
    exact: true,
  },
  {
    name: "Utilisateurs",
    href: "/dashboard/users",
    icon: Users,
  },
  {
    name: "Offres d'emploi",
    href: "/dashboard/jobs",
    icon: Briefcase,
  },
  {
    name: "Candidatures",
    href: "/dashboard/applications",
    icon: MessageSquare,
  },
  {
    name: "Analytiques",
    href: "/dashboard/analytics",
    icon: TrendingUp,
  },
  {
    name: "Rapports",
    href: "/dashboard/reports",
    icon: BarChart3,
  },
  {
    name: "Modération",
    href: "/dashboard/moderation",
    icon: Shield,
  },
  {
    name: "Paramètres",
    href: "/dashboard/settings",
    icon: Settings,
  },
]

export default function DashboardSidebar({
  lang,
  isMobileOpen = false,
  onMobileClose
}: DashboardSidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const pathname = usePathname()

  const isActive = (href: string, exact?: boolean) => {
    if (exact) {
      return pathname === `/${lang}${href}`
    }
    return pathname.startsWith(`/${lang}${href}`)
  }

  return (
    <>
      {/* Mobile Overlay */}
      {isMobileOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={onMobileClose}
          aria-hidden="true"
        />
      )}

      {/* Sidebar */}
      <Card
        id="mobile-sidebar"
        className={cn(
          "h-full rounded-none border-r border-divider transition-all duration-300",
          "fixed lg:relative z-50 lg:z-auto",
          isCollapsed ? "w-16" : "w-64",
          // Mobile responsive
          "lg:translate-x-0",
          isMobileOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"
        )}
        role="navigation"
        aria-label="Dashboard navigation sidebar"
        aria-hidden={isMobileOpen ? undefined : "true"}
      >
        <div className="flex h-full flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-divider">
            {!isCollapsed && (
              <div className="flex items-center space-x-2">
                <Icons.logo className="size-6 text-primary" />
                <span className="font-semibold text-foreground">ProAmLink</span>
              </div>
            )}
            <Button
              isIconOnly
              variant="ghost"
              size="sm"
              onPress={() => setIsCollapsed(!isCollapsed)}
              className="ml-auto"
              aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
              aria-expanded={!isCollapsed}
            >
              {isCollapsed ? (
                <ChevronRight className="size-4" aria-hidden="true" />
              ) : (
                <ChevronLeft className="size-4" aria-hidden="true" />
              )}
            </Button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2" role="navigation" aria-label="Dashboard navigation">
            {navigationItems.map((item) => {
              const Icon = item.icon
              const active = isActive(item.href, item.exact)

              return (
                <Link
                  key={item.href}
                  href={`/${lang}${item.href}`}
                  className={cn(
                    "flex items-center space-x-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors",
                    "hover:bg-default-100 hover:text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",
                    isCollapsed ? "justify-center" : "",
                    active
                      ? "bg-primary text-primary-foreground"
                      : "text-default-600"
                  )}
                  aria-current={active ? "page" : undefined}
                  title={isCollapsed ? item.name : undefined}
                  onClick={onMobileClose}
                >
                  <Icon className={cn("h-5 w-5 flex-shrink-0")} aria-hidden="true" />
                  {!isCollapsed && <span>{item.name}</span>}
                  {isCollapsed && <span className="sr-only">{item.name}</span>}
                </Link>
              )
            })}
          </nav>

          {/* Footer */}
          {/* <div className="p-4 border-t border-divider">
            {!isCollapsed && (
              <div className="text-xs text-default-500">
                <p>Dashboard Admin</p>
                <p>Version 1.0.0</p>
              </div>
            )}
          </div> */}
        </div>
      </Card>
    </>
  )
}
