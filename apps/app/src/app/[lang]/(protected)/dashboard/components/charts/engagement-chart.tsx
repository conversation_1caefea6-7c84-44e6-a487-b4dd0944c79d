"use client"

import {
  Area,
  AreaChart,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis
} from "recharts"

import { Card, CardBody, CardHeader } from "@nextui-org/card"

interface EngagementData {
  date: string
  posts: number
  likes: number
  comments: number
  shares: number
}

interface EngagementChartProps {
  data: EngagementData[]
  isLoading?: boolean
}

export default function EngagementChart({ data, isLoading }: EngagementChartProps) {
  if (isLoading) {
    return (
      <Card className="h-96">
        <CardHeader>
          <h3 className="text-lg font-semibold">Engagement des utilisateurs</h3>
        </CardHeader>
        <CardBody>
          <div className="flex items-center justify-center h-full">
            <div className="animate-pulse">
              <div className="h-4 bg-default-200 rounded w-48 mb-4"></div>
              <div className="space-y-2">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="h-6 bg-default-200 rounded w-full"></div>
                ))}
              </div>
            </div>
          </div>
        </CardBody>
      </Card>
    )
  }

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr)
    return date.toLocaleDateString('fr-FR', {
      month: 'short',
      day: 'numeric'
    })
  }

  const CustomTooltip = ({ active, payload, label }: { active?: boolean; payload?: Array<{ name: string; value: number; color: string }>; label?: string }) => {
    if (active && payload && payload.length && label) {
      const total = payload.reduce((sum: number, entry) => sum + entry.value, 0)

      return (
        <div className="bg-content1 border border-divider rounded-lg p-3 shadow-lg">
          <p className="text-sm font-medium mb-2">
            {formatDate(label)}
          </p>
          <p className="text-sm font-medium mb-1">
            Total: {total} interactions
          </p>
          {payload.map((entry, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {entry.value}
            </p>
          ))}
        </div>
      )
    }
    return null
  }

  return (
    <Card className="h-96">
      <CardHeader>
        <h3 className="text-lg font-semibold">Engagement des utilisateurs</h3>
        <p className="text-sm text-default-600">
          Activité des utilisateurs sur la plateforme
        </p>
      </CardHeader>
      <CardBody>
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <defs>
              <linearGradient id="colorPosts" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="hsl(var(--nextui-primary))" stopOpacity={0.8} />
                <stop offset="95%" stopColor="hsl(var(--nextui-primary))" stopOpacity={0.1} />
              </linearGradient>
              <linearGradient id="colorLikes" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="hsl(var(--nextui-success))" stopOpacity={0.8} />
                <stop offset="95%" stopColor="hsl(var(--nextui-success))" stopOpacity={0.1} />
              </linearGradient>
              <linearGradient id="colorComments" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="hsl(var(--nextui-warning))" stopOpacity={0.8} />
                <stop offset="95%" stopColor="hsl(var(--nextui-warning))" stopOpacity={0.1} />
              </linearGradient>
              <linearGradient id="colorShares" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="hsl(var(--nextui-secondary))" stopOpacity={0.8} />
                <stop offset="95%" stopColor="hsl(var(--nextui-secondary))" stopOpacity={0.1} />
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis
              dataKey="date"
              tickFormatter={formatDate}
              className="text-xs"
            />
            <YAxis className="text-xs" />
            <Tooltip content={<CustomTooltip />} />
            <Area
              type="monotone"
              dataKey="posts"
              stackId="1"
              stroke="hsl(var(--nextui-primary))"
              fill="url(#colorPosts)"
              name="Publications"
            />
            <Area
              type="monotone"
              dataKey="likes"
              stackId="1"
              stroke="hsl(var(--nextui-success))"
              fill="url(#colorLikes)"
              name="J'aime"
            />
            <Area
              type="monotone"
              dataKey="comments"
              stackId="1"
              stroke="hsl(var(--nextui-warning))"
              fill="url(#colorComments)"
              name="Commentaires"
            />
            <Area
              type="monotone"
              dataKey="shares"
              stackId="1"
              stroke="hsl(var(--nextui-secondary))"
              fill="url(#colorShares)"
              name="Partages"
            />
          </AreaChart>
        </ResponsiveContainer>
      </CardBody>
    </Card>
  )
}
