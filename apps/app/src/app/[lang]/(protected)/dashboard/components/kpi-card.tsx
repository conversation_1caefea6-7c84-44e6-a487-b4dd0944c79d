"use client"

import { ReactNode } from "react"
import { Minus, TrendingDown, TrendingUp } from "lucide-react"

import { cn } from "@/lib/utils"
import { Card, CardBody } from "@nextui-org/card"
import { Chip } from "@nextui-org/chip"

interface KPICardProps {
  title: string
  value: string | number
  icon: ReactNode
  trend?: {
    value: number
    label: string
  }
  description?: string
  color?: "primary" | "secondary" | "success" | "warning" | "danger"
  isLoading?: boolean
}

export default function KPICard({
  title,
  value,
  icon,
  trend,
  description,
  color = "primary",
  isLoading = false,
}: KPICardProps) {
  const getTrendIcon = () => {
    if (!trend) return null

    if (trend.value > 0) {
      return <TrendingUp className="size-3" />
    } else if (trend.value < 0) {
      return <TrendingDown className="size-3" />
    } else {
      return <Minus className="size-3" />
    }
  }

  const getTrendColor = () => {
    if (!trend) return "default"

    if (trend.value > 0) {
      return "success"
    } else if (trend.value < 0) {
      return "danger"
    } else {
      return "default"
    }
  }

  const formatValue = (val: string | number) => {
    if (typeof val === "number") {
      if (val >= 1000000) {
        return `${(val / 1000000).toFixed(1)}M`
      } else if (val >= 1000) {
        return `${(val / 1000).toFixed(1)}K`
      }
      return val.toLocaleString()
    }
    return val
  }

  if (isLoading) {
    return (
      <Card className="h-full">
        <CardBody className="p-6">
          <div className="animate-pulse">
            <div className="flex items-center justify-between mb-4">
              <div className="h-4 bg-default-200 rounded w-24"></div>
              <div className="size-8 bg-default-200 rounded"></div>
            </div>
            <div className="h-8 bg-default-200 rounded w-16 mb-2"></div>
            <div className="h-3 bg-default-200 rounded w-20"></div>
          </div>
        </CardBody>
      </Card>
    )
  }

  return (
    <Card className="h-full hover:shadow-lg transition-shadow">
      <CardBody className="p-4 sm:p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xs sm:text-sm font-medium text-default-600 truncate pr-2">
            {title}
          </h3>
          <div className={cn(
            "p-2 rounded-lg flex-shrink-0",
            color === "primary" && "bg-primary/10 text-primary",
            color === "secondary" && "bg-secondary/10 text-secondary",
            color === "success" && "bg-success/10 text-success",
            color === "warning" && "bg-warning/10 text-warning",
            color === "danger" && "bg-danger/10 text-danger"
          )}>
            {icon}
          </div>
        </div>

        <div className="space-y-2">
          <p className="text-xl sm:text-2xl font-bold text-foreground">
            {formatValue(value)}
          </p>

          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
            {trend && (
              <Chip
                size="sm"
                variant="flat"
                color={getTrendColor()}
                startContent={getTrendIcon()}
                classNames={{
                  base: "h-6 w-fit",
                  content: "text-xs font-medium",
                }}
              >
                {Math.abs(trend.value)}% {trend.label}
              </Chip>
            )}

            {description && (
              <p className="text-xs text-default-500 sm:ml-auto">
                {description}
              </p>
            )}
          </div>
        </div>
      </CardBody>
    </Card>
  )
}
