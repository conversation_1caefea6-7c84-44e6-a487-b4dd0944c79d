"use client"

import { Key, useCallback } from "react"
import {
  Calendar,
  Edit,
  Eye,
  Mail,
  MoreVertical,
  Phone,
  Trash2,
  <PERSON>r<PERSON><PERSON><PERSON>,
  User<PERSON>
} from "lucide-react"

import { Avatar } from "@nextui-org/avatar"
import { <PERSON><PERSON> } from "@nextui-org/button"
import { Chip } from "@nextui-org/chip"
import {
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownTrigger
} from "@nextui-org/dropdown"
import { Pagination } from "@nextui-org/pagination"
import { Spinner } from "@nextui-org/spinner"
import {
  getKeyValue,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from "@nextui-org/table"
import { Tooltip } from "@nextui-org/tooltip"
import { UserRole } from "@prisma/client"

interface User {
  id: string
  name: string | null
  email: string | null
  username: string | null
  role: UserRole
  emailVerified: Date | null
  phoneVerified: Date | null
  createdAt: Date
  updatedAt: Date
  profilePicture: {
    id: string
    key: string
    endpoint: string
  } | null
  athleteProfile: {
    id: string
    firstName: string
    lastName: string
    isAvailable: boolean
  } | null
  recruiterProfile: {
    id: string
    companyName: string | null
    jobTitle: string | null
  } | null
  _count: {
    posts: number
    applications: number
    sentInvitations: number
  }
}

interface Pagination {
  page: number
  limit: number
  total: number
  totalPages: number
}

interface UsersTableProps {
  users: User[]
  pagination?: Pagination
  isLoading: boolean
  onPageChange: (page: number) => void
  onSort: (column: string, order: string) => void
  onUpdateUserStatus: (userId: string, isActive: boolean) => void
  isUpdatingStatus: boolean
}

const columns = [
  { name: "UTILISATEUR", uid: "user", sortable: true },
  { name: "RÔLE", uid: "role", sortable: true },
  { name: "STATUT", uid: "status" },
  { name: "VÉRIFICATION", uid: "verification" },
  { name: "ACTIVITÉ", uid: "activity" },
  { name: "DATE CRÉATION", uid: "createdAt", sortable: true },
  { name: "ACTIONS", uid: "actions" },
]

export default function UsersTable({
  users,
  pagination,
  isLoading,
  onPageChange,
  onUpdateUserStatus,
}: UsersTableProps) {
  const getRoleColor = (role: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return "danger"
      case UserRole.ATHLETE:
        return "success"
      case UserRole.RECRUITER:
        return "secondary"
      default:
        return "primary"
    }
  }

  const getRoleLabel = (role: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return "Admin"
      case UserRole.ATHLETE:
        return "Athlète"
      case UserRole.RECRUITER:
        return "Recruteur"
      default:
        return "Athlète"
    }
  }

  const isUserActive = (user: User) => {
    // Simple check: user is active if updated in last 30 days
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
    return new Date(user.updatedAt) > thirtyDaysAgo
  }

  const getUserDisplayName = (user: User) => {
    if (user.athleteProfile) {
      return `${user.athleteProfile.firstName} ${user.athleteProfile.lastName}`
    }
    return user.name || user.username || user.email || "Utilisateur"
  }

  const renderCell = useCallback((user: User, columnKey: Key) => {
    const cellValue = getKeyValue(user, columnKey as string)

    switch (columnKey) {
      case "user":
        return (
          <div className="flex items-center gap-3">
            <Avatar
              src={user.profilePicture ? `${user.profilePicture.endpoint}/${user.profilePicture.key}` : undefined}
              name={getUserDisplayName(user)}
              size="sm"
            />
            <div className="flex flex-col">
              <p className="text-sm font-medium">{getUserDisplayName(user)}</p>
              <p className="text-xs text-default-500">{user.email}</p>
              {user.username && (
                <p className="text-xs text-default-400">@{user.username}</p>
              )}
            </div>
          </div>
        )

      case "role":
        return (
          <Chip
            color={getRoleColor(user.role)}
            size="sm"
            variant="flat"
          >
            {getRoleLabel(user.role)}
          </Chip>
        )

      case "status":
        const active = isUserActive(user)
        return (
          <Chip
            color={active ? "success" : "default"}
            size="sm"
            variant="flat"
            startContent={active ? <UserCheck className="size-3" /> : <UserX className="size-3" />}
          >
            {active ? "Actif" : "Inactif"}
          </Chip>
        )

      case "verification":
        return (
          <div className="flex gap-1">
            <Tooltip content={user.emailVerified ? "Email vérifié" : "Email non vérifié"}>
              <Chip
                color={user.emailVerified ? "success" : "default"}
                size="sm"
                variant="flat"
                startContent={<Mail className="size-3" />}
              >
                Email
              </Chip>
            </Tooltip>
            <Tooltip content={user.phoneVerified ? "Téléphone vérifié" : "Téléphone non vérifié"}>
              <Chip
                color={user.phoneVerified ? "success" : "default"}
                size="sm"
                variant="flat"
                startContent={<Phone className="size-3" />}
              >
                Tel
              </Chip>
            </Tooltip>
          </div>
        )

      case "activity":
        return (
          <div className="text-xs">
            <p>{user._count.posts} posts</p>
            <p>{user._count.applications} candidatures</p>
            <p>{user._count.sentInvitations} invitations</p>
          </div>
        )

      case "createdAt":
        return (
          <div className="flex items-center gap-1 text-xs">
            <Calendar className="size-3" />
            {new Date(user.createdAt).toLocaleDateString('fr-FR')}
          </div>
        )

      case "actions":
        return (
          <Dropdown>
            <DropdownTrigger>
              <Button isIconOnly size="sm" variant="light">
                <MoreVertical className="size-4" />
              </Button>
            </DropdownTrigger>
            <DropdownMenu>
              <DropdownItem
                key="view"
                startContent={<Eye className="size-4" />}
              >
                Voir le profil
              </DropdownItem>
              <DropdownItem
                key="edit"
                startContent={<Edit className="size-4" />}
              >
                Modifier
              </DropdownItem>
              <DropdownItem
                key="toggle-status"
                startContent={isUserActive(user) ? <UserX className="size-4" /> : <UserCheck className="size-4" />}
                onPress={() => onUpdateUserStatus(user.id, !isUserActive(user))}
              >
                {isUserActive(user) ? "Désactiver" : "Activer"}
              </DropdownItem>
              <DropdownItem
                key="delete"
                color="danger"
                startContent={<Trash2 className="size-4" />}
              >
                Supprimer
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
        )

      default:
        return cellValue
    }
  }, [onUpdateUserStatus])

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <Table aria-label="Users table">
        <TableHeader columns={columns}>
          {(column) => (
            <TableColumn
              key={column.uid}
              allowsSorting={column.sortable}
            >
              {column.name}
            </TableColumn>
          )}
        </TableHeader>
        <TableBody items={users} emptyContent="Aucun utilisateur trouvé">
          {(item) => (
            <TableRow key={item.id}>
              {(columnKey) => <TableCell>{renderCell(item, columnKey)}</TableCell>}
            </TableRow>
          )}
        </TableBody>
      </Table>

      {pagination && pagination.totalPages > 1 && (
        <div className="flex justify-center">
          <Pagination
            total={pagination.totalPages}
            page={pagination.page}
            onChange={onPageChange}
            showControls
            showShadow
          />
        </div>
      )}
    </div>
  )
}
