import { Metadata } from "next"

import { Locale } from "@/lib/i18n-config"

import UsersManagement from "./components/users-management"

export const metadata: Metadata = {
  title: "Gestion des utilisateurs - Dashboard",
  description: "Interface de gestion des utilisateurs ProAmLink",
}

export default async function UsersPage({
  params: { lang: _ },
}: {
  params: {
    lang: Locale
  }
}) {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold text-foreground">
          Gestion des utilisateurs
        </h1>
        <p className="text-default-600 mt-2">
          <PERSON><PERSON><PERSON> les utilisateurs, athlètes et recruteurs de la plateforme
        </p>
      </div>

      {/* Users Management */}
      <UsersManagement />
    </div>
  )
}
