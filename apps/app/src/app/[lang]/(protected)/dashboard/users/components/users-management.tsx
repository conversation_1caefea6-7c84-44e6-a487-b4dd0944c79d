"use client"

import { use<PERSON><PERSON>back, useMemo, useState } from "react"
import { Download, Filter, Search, UserPlus } from "lucide-react"

import { trpc } from "@/lib/trpc/client"
import { <PERSON><PERSON> } from "@nextui-org/button"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { Chip } from "@nextui-org/chip"
import { Input } from "@nextui-org/input"
import { Select, SelectItem } from "@nextui-org/select"
import { UserRole } from "@prisma/client"

import UsersTable from "./users-table"

export default function UsersManagement() {
  const [page, setPage] = useState(1)
  const [search, setSearch] = useState("")
  const [roleFilter, setRoleFilter] = useState<UserRole | "">("")
  const [sortBy, setSortBy] = useState<"createdAt" | "name" | "email" | "role">("createdAt")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc")
  const [isActiveFilter, setIsActiveFilter] = useState<boolean | undefined>(undefined)

  const limit = 20

  const { data: usersData, isLoading, refetch } = trpc.dashboardUser.getUsersList.useQuery({
    page,
    limit,
    search: search || undefined,
    role: roleFilter || undefined,
    sortBy,
    sortOrder,
    isActive: isActiveFilter,
  })

  const updateUserStatusMutation = trpc.dashboardUser.updateUserStatus.useMutation({
    onSuccess: () => {
      refetch()
    },
  })

  const handleSearch = useCallback((value: string) => {
    setSearch(value)
    setPage(1) // Reset to first page when searching
  }, [])

  const handleRoleFilter = useCallback((role: string) => {
    setRoleFilter(role as UserRole | "")
    setPage(1)
  }, [])

  const handleActiveFilter = useCallback((value: string) => {
    if (value === "all") {
      setIsActiveFilter(undefined)
    } else {
      setIsActiveFilter(value === "active")
    }
    setPage(1)
  }, [])

  const handleSort = useCallback((column: string, order: string) => {
    setSortBy(column as "createdAt" | "name" | "email" | "role")
    setSortOrder(order as "asc" | "desc")
    setPage(1)
  }, [])

  const handleUpdateUserStatus = useCallback(async (userId: string, isActive: boolean) => {
    try {
      await updateUserStatusMutation.mutateAsync({
        userId,
        isActive,
      })
    } catch (error) {
      // Error handling - could show toast notification here
    }
  }, [updateUserStatusMutation])

  const handleExport = useCallback(() => {
    // TODO: Implement export functionality
    // Export functionality will be implemented here
  }, [])

  const roleOptions = [
    { key: "", label: "Tous les rôles" },
    { key: UserRole.ATHLETE, label: "Athlètes" },
    { key: UserRole.RECRUITER, label: "Recruteurs" },
    { key: UserRole.ADMIN, label: "Administrateurs" },
  ]

  const activeOptions = [
    { key: "all", label: "Tous" },
    { key: "active", label: "Actifs" },
    { key: "inactive", label: "Inactifs" },
  ]

  const stats = useMemo(() => {
    if (!usersData) return null

    return {
      total: usersData.pagination.total,
      showing: usersData.users.length,
      pages: usersData.pagination.totalPages,
    }
  }, [usersData])

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardBody className="text-center">
              <div className="text-2xl font-bold text-primary">{stats.total}</div>
              <div className="text-sm text-default-600">Total utilisateurs</div>
            </CardBody>
          </Card>
          <Card>
            <CardBody className="text-center">
              <div className="text-2xl font-bold text-success">{stats.showing}</div>
              <div className="text-sm text-default-600">Affichés</div>
            </CardBody>
          </Card>
          <Card>
            <CardBody className="text-center">
              <div className="text-2xl font-bold text-secondary">{stats.pages}</div>
              <div className="text-sm text-default-600">Pages</div>
            </CardBody>
          </Card>
        </div>
      )}

      {/* Filters and Actions */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Filtres et actions</h3>
        </CardHeader>
        <CardBody>
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <Input
                placeholder="Rechercher par nom, email ou nom d'utilisateur..."
                value={search}
                onValueChange={handleSearch}
                startContent={<Search className="size-4 text-default-400" />}
                variant="bordered"
                classNames={{
                  input: "text-sm",
                }}
              />
            </div>

            {/* Role Filter */}
            <Select
              placeholder="Filtrer par rôle"
              selectedKeys={roleFilter ? [roleFilter] : [""]}
              onSelectionChange={(keys) => {
                const role = Array.from(keys)[0] as string
                handleRoleFilter(role)
              }}
              className="w-full lg:w-48"
              variant="bordered"
              startContent={<Filter className="size-4" />}
            >
              {roleOptions.map((option) => (
                <SelectItem key={option.key} value={option.key}>
                  {option.label}
                </SelectItem>
              ))}
            </Select>

            {/* Active Filter */}
            <Select
              placeholder="Filtrer par statut"
              selectedKeys={isActiveFilter === undefined ? ["all"] : [isActiveFilter ? "active" : "inactive"]}
              onSelectionChange={(keys) => {
                const status = Array.from(keys)[0] as string
                handleActiveFilter(status)
              }}
              className="w-full lg:w-48"
              variant="bordered"
            >
              {activeOptions.map((option) => (
                <SelectItem key={option.key} value={option.key}>
                  {option.label}
                </SelectItem>
              ))}
            </Select>

            {/* Actions */}
            <div className="flex gap-2">
              <Button
                variant="bordered"
                startContent={<Download className="size-4" />}
                onPress={handleExport}
              >
                Exporter
              </Button>
              <Button
                color="primary"
                startContent={<UserPlus className="size-4" />}
              >
                Ajouter
              </Button>
            </div>
          </div>

          {/* Active Filters */}
          <div className="flex flex-wrap gap-2 mt-4">
            {search && (
              <Chip
                onClose={() => handleSearch("")}
                variant="flat"
                color="primary"
              >
                Recherche: {search}
              </Chip>
            )}
            {roleFilter && (
              <Chip
                onClose={() => handleRoleFilter("")}
                variant="flat"
                color="secondary"
              >
                Rôle: {roleOptions.find(r => r.key === roleFilter)?.label}
              </Chip>
            )}
            {isActiveFilter !== undefined && (
              <Chip
                onClose={() => handleActiveFilter("all")}
                variant="flat"
                color="success"
              >
                Statut: {isActiveFilter ? "Actifs" : "Inactifs"}
              </Chip>
            )}
          </div>
        </CardBody>
      </Card>

      {/* Users Table */}
      <UsersTable
        users={usersData?.users || []}
        pagination={usersData?.pagination}
        isLoading={isLoading}
        onPageChange={setPage}
        onSort={handleSort}
        onUpdateUserStatus={handleUpdateUserStatus}
        isUpdatingStatus={updateUserStatusMutation.isPending}
      />
    </div>
  )
}
