import { Metadata } from "next"

import { Locale } from "@/lib/i18n-config"

import AnalyticsOverview from "./components/analytics-overview"

export const metadata: Metadata = {
  title: "Analytiques - Dashboard",
  description: "Analyses détaillées et métriques de performance",
}

export default async function AnalyticsPage({
  params: { lang: _ },
}: {
  params: {
    lang: Locale
  }
}) {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold text-foreground">
          Analytiques
        </h1>
        <p className="text-default-600 mt-2">
          Analyses détaillées et métriques de performance de la plateforme
        </p>
      </div>

      {/* Analytics Content */}
      <AnalyticsOverview />
    </div>
  )
}
