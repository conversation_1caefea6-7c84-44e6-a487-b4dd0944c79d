"use client"

import { useState } from "react"
import { Target, Users } from "lucide-react"

import { trpc } from "@/lib/trpc/client"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { Select, SelectItem } from "@nextui-org/select"
import { Tab, Tabs } from "@nextui-org/tabs"

import ApplicationTrendsChart from "../../components/charts/application-trends-chart"
import UserDistributionChart from "../../components/charts/user-distribution-chart"
import UserGrowthChart from "../../components/charts/user-growth-chart"
import KPICard from "../../components/kpi-card"

export default function AnalyticsOverview() {
  const [period, setPeriod] = useState<"7d" | "30d" | "90d" | "1y">("30d")
  const [granularity, setGranularity] = useState<"day" | "week" | "month">("day")

  const { data: userAnalytics, isLoading: isLoadingUserAnalytics } = trpc.dashboardGeneral.getUserAnalytics.useQuery({
    period,
    granularity,
  })

  const { data: applicationAnalytics, isLoading: isLoadingApplicationAnalytics } = trpc.dashboardGeneral.getApplicationAnalytics.useQuery({
    period,
    granularity,
  })

  const periodOptions = [
    { key: "7d", label: "7 derniers jours" },
    { key: "30d", label: "30 derniers jours" },
    { key: "90d", label: "90 derniers jours" },
    { key: "1y", label: "1 an" },
  ]

  const granularityOptions = [
    { key: "day", label: "Par jour" },
    { key: "week", label: "Par semaine" },
    { key: "month", label: "Par mois" },
  ]

  return (
    <div className="space-y-6">
      {/* Controls */}
      <Card>
        <CardBody>
          <div className="flex flex-col sm:flex-row gap-4">
            <Select
              size="sm"
              placeholder="Sélectionner une période"
              selectedKeys={[period]}
              onSelectionChange={(keys) => {
                const selectedPeriod = Array.from(keys)[0] as string
                setPeriod(selectedPeriod as "7d" | "30d" | "90d" | "1y")
              }}
              className="w-full sm:w-48"
            >
              {periodOptions.map((option) => (
                <SelectItem key={option.key} value={option.key}>
                  {option.label}
                </SelectItem>
              ))}
            </Select>

            <Select
              size="sm"
              placeholder="Granularité"
              selectedKeys={[granularity]}
              onSelectionChange={(keys) => {
                const selectedGranularity = Array.from(keys)[0] as string
                setGranularity(selectedGranularity as "day" | "week" | "month")
              }}
              className="w-full sm:w-48"
            >
              {granularityOptions.map((option) => (
                <SelectItem key={option.key} value={option.key}>
                  {option.label}
                </SelectItem>
              ))}
            </Select>
          </div>
        </CardBody>
      </Card>

      {/* Analytics Tabs */}
      <Tabs aria-label="Analytics sections" className="w-full">
        <Tab key="users" title="Utilisateurs">
          <div className="space-y-6">
            {/* User KPIs */}
            {userAnalytics && (
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <KPICard
                  title="Total Athlètes"
                  value={userAnalytics.usersByRole.athletes}
                  icon={<Target className="size-5" />}
                  color="success"
                />
                <KPICard
                  title="Total Recruteurs"
                  value={userAnalytics.usersByRole.recruiters}
                  icon={<Users className="size-5" />}
                  color="secondary"
                />
                <KPICard
                  title="Administrateurs"
                  value={userAnalytics.usersByRole.admins}
                  icon={<Users className="size-5" />}
                  color="danger"
                />

              </div>
            )}

            {/* User Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <UserGrowthChart
                data={userAnalytics?.userRegistrations || []}
                isLoading={isLoadingUserAnalytics}
              />
              <UserDistributionChart
                data={userAnalytics?.usersByRole || { athletes: 0, recruiters: 0, admins: 0 }}
                isLoading={isLoadingUserAnalytics}
              />
            </div>

            {/* Additional User Analytics */}
            {userAnalytics && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <h3 className="text-lg font-semibold">Top 10 pays</h3>
                  </CardHeader>
                  <CardBody>
                    <div className="space-y-3">
                      {userAnalytics.topCountries.map((country, index) => (
                        <div key={country.country} className="flex justify-between items-center">
                          <div className="flex items-center space-x-2">
                            <span className="text-sm font-medium text-default-600">
                              #{index + 1}
                            </span>
                            <span className="text-sm">{country.country}</span>
                          </div>
                          <span className="text-sm font-semibold text-primary">
                            {country.count}
                          </span>
                        </div>
                      ))}
                    </div>
                  </CardBody>
                </Card>

                <Card>
                  <CardHeader>
                    <h3 className="text-lg font-semibold">Statut de vérification</h3>
                  </CardHeader>
                  <CardBody>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Email vérifié</span>
                        <div className="flex items-center space-x-2">
                          <div className="w-20 bg-default-200 rounded-full h-2">
                            <div
                              className="bg-success h-2 rounded-full"
                              style={{
                                width: `${(userAnalytics.verificationStats.emailVerified / (userAnalytics.verificationStats.emailVerified + userAnalytics.verificationStats.unverified)) * 100}%`
                              }}
                            ></div>
                          </div>
                          <span className="text-sm font-semibold">
                            {userAnalytics.verificationStats.emailVerified}
                          </span>
                        </div>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Téléphone vérifié</span>
                        <div className="flex items-center space-x-2">
                          <div className="w-20 bg-default-200 rounded-full h-2">
                            <div
                              className="bg-secondary h-2 rounded-full"
                              style={{
                                width: `${(userAnalytics.verificationStats.phoneVerified / (userAnalytics.verificationStats.phoneVerified + userAnalytics.verificationStats.unverified)) * 100}%`
                              }}
                            ></div>
                          </div>
                          <span className="text-sm font-semibold">
                            {userAnalytics.verificationStats.phoneVerified}
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </div>
            )}
          </div>
        </Tab>

        <Tab key="applications" title="Candidatures">
          <div className="space-y-6">
            {/* Application Charts */}
            <div className="grid grid-cols-1 gap-6">
              <ApplicationTrendsChart
                data={applicationAnalytics?.applicationTrends || []}
                isLoading={isLoadingApplicationAnalytics}
              />
            </div>

            {/* Application Analytics */}
            {applicationAnalytics && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <h3 className="text-lg font-semibold">Sports les plus populaires</h3>
                  </CardHeader>
                  <CardBody>
                    <div className="space-y-3">
                      {applicationAnalytics.topSports.map((sport) => (
                        <div key={sport.sport} className="space-y-1">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">{sport.sport}</span>
                            <span className="text-xs text-default-500">
                              {sport.applications} candidatures
                            </span>
                          </div>
                          <div className="w-full bg-default-200 rounded-full h-2">
                            <div
                              className="bg-primary h-2 rounded-full"
                              style={{
                                width: `${(sport.applications / Math.max(...applicationAnalytics.topSports.map(s => s.applications))) * 100}%`
                              }}
                            ></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardBody>
                </Card>

                <Card>
                  <CardHeader>
                    <h3 className="text-lg font-semibold">Taux de conversion</h3>
                  </CardHeader>
                  <CardBody>
                    <div className="space-y-4">
                      <div className="text-center">
                        <div className="text-3xl font-bold text-primary mb-1">
                          {applicationAnalytics.conversionRates.overallSuccess}%
                        </div>
                        <div className="text-sm text-default-600">Taux de succès global</div>
                      </div>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Candidature → Examen</span>
                          <span className="text-sm font-semibold text-success">
                            {applicationAnalytics.conversionRates.applicationToReview}%
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Examen → Acceptation</span>
                          <span className="text-sm font-semibold text-warning">
                            {applicationAnalytics.conversionRates.reviewToAcceptance}%
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </div>
            )}
          </div>
        </Tab>
      </Tabs>
    </div>
  )
}
