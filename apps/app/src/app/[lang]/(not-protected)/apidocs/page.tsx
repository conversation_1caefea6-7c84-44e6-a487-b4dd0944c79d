import { Metadata } from "next"
import dynamic from "next/dynamic"

import { env } from "@/lib/env"

// Import dynamique du composant Swagger UI pour éviter les problèmes SSR
const SwaggerUIComponent = dynamic(
  () => import("@/components/swagger-ui/swagger-ui-component"),
  {
    ssr: false,
    loading: () => (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full size-12 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Chargement de la documentation...</span>
      </div>
    ),
  }
)

export const metadata: Metadata = {
  title: "Documentation API - ProAmLink",
  description: "Interface interactive pour explorer et tester l'API REST de ProAmLink",
}

/**
 * Page de documentation interactive Swagger UI
 * 
 * Cette page affiche la documentation interactive de l'API ProAmLink
 * en utilisant Swagger UI. Elle consomme automatiquement la spécification
 * OpenAPI générée depuis l'endpoint /api/endpoints.json.
 * 
 * Fonctionnalités :
 * - Documentation interactive de tous les endpoints
 * - Test direct des endpoints depuis l'interface
 * - Validation des schémas de données
 * - Exemples de requêtes et réponses
 * - Support de l'authentification Bearer
 */

/**
 * Composant principal de la page de documentation
 */
export default function ApiDocsPage() {
  const baseUrl = env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000"
  const openApiUrl = `${baseUrl}/api/endpoints.json`
  const currentDate = new Date().toLocaleDateString("fr-FR")

  return (
    <div className="min-h-screen bg-white">
      {/* Header personnalisé */}
      <div className="bg-gray-900 text-white py-6">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl font-bold">Documentation API ProAmLink</h1>
          <p className="text-gray-300 mt-2">
            Interface interactive pour explorer et tester l&apos;API REST de ProAmLink
          </p>
        </div>
      </div>

      {/* Contenu principal avec iframe Swagger UI */}
      <div className="container mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          {/* Instructions d'utilisation */}
          <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6">
            <div className="flex">
              <div className="shrink-0">
                <svg className="size-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-blue-700">
                  <strong>Instructions :</strong> Utilisez le bouton &quot;Authorize&quot; pour configurer votre token Bearer
                  et tester les endpoints protégés. La spécification OpenAPI est générée automatiquement
                  depuis les procédures tRPC.
                </p>
              </div>
            </div>
          </div>

          {/* Composant Swagger UI local */}
          <div className="relative min-h-[800px]">
            <SwaggerUIComponent openApiUrl={openApiUrl} />
          </div>
        </div>
      </div>

      {/* Footer avec informations utiles */}
      <div className="bg-gray-50 border-t border-gray-200 py-8 mt-12">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-3 gap-8">
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">Ressources utiles</h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>
                  <a
                    href="/api/endpoints.json"
                    className="hover:text-blue-600"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Spécification OpenAPI (JSON)
                  </a>
                </li>
                <li>
                  <a
                    href="https://swagger.io/docs/"
                    className="hover:text-blue-600"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Documentation Swagger
                  </a>
                </li>
                <li>
                  <a
                    href="https://trpc.io/docs"
                    className="hover:text-blue-600"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Documentation tRPC
                  </a>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold text-gray-900 mb-3">Support</h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>Version API: 1.0.0</li>
                <li>Dernière mise à jour: {currentDate}</li>
                <li>Environnement: {env.ENV || "development"}</li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold text-gray-900 mb-3">Authentification</h3>
              <p className="text-sm text-gray-600">
                Utilisez le bouton &quot;Authorize&quot; dans l&apos;interface Swagger
                pour configurer votre token Bearer et accéder aux endpoints protégés.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Liens directs pour les développeurs */}
      <div className="bg-gray-900 text-white py-6">
        <div className="container mx-auto px-4">
          <h3 className="text-lg font-semibold mb-4">Liens rapides pour les développeurs</h3>
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-gray-300 mb-2">Endpoints d&apos;exemple</h4>
              <ul className="space-y-1 text-sm text-gray-400">
                <li>
                  <code className="bg-gray-800 px-2 py-1 rounded">GET /api/health</code> - Santé de l&apos;API
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-300 mb-2">Génération SDK Flutter</h4>
              <div className="text-sm text-gray-400">
                <p className="mb-2">Pour générer le SDK Flutter :</p>
                <code className="bg-gray-800 px-2 py-1 rounded block">
                  npm run generate:flutter-sdk
                </code>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
