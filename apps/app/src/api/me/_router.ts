import { z } from "zod"

import {
  deleteAccountResponseSchema,
  deleteSessionResponseSchema,
  deleteSessionSchema,
  forgotPasswordResponseSchema,
  forgotPasswordSchema,
  getAccountResponseSchema,
  getActiveSessionsResponseSchema,
  getActiveSessionsSchema,
  resetPasswordResponseSchema,
  resetPasswordSchema,
  sendVerificationEmailResponseSchema,
  sendVerificationEmailSchema,
  updateUserResponseSchema,
  updateUserSchema,
  verifyEmailResponseSchema,
  verifyEmailSchema,
} from "@/api/me/schemas"
import {
  authenticatedNoEmailVerificationProcedure,
  authenticatedProcedure,
  publicProcedure,
  router,
} from "@/lib/server/trpc"

import { sendVerificationEmail, verifyEmail } from "./email/mutations"
import { forgotPassword, resetPassword } from "./password/mutations"
import { deleteSession } from "./sessions/mutations"
import { getActiveSessions } from "./sessions/queries"
import { deleteAccount, updateUser } from "./mutations"
import { getAccount } from "./queries"

export const meRouter = router({
  updateUser: authenticatedProcedure
    .meta({
      openapi: {
        method: "PUT",
        path: "/api/rest/me/profile",
        tags: ["Profil utilisateur"],
        summary: "Mise à jour du profil utilisateur",
        description: "Met à jour les informations du profil de l'utilisateur connecté",
        protect: true,
      },
    })
    .input(updateUserSchema())
    .output(updateUserResponseSchema())
    .mutation(updateUser),
  getActiveSessions: authenticatedNoEmailVerificationProcedure
    .meta({
      openapi: {
        method: "GET",
        path: "/api/rest/me/sessions",
        tags: ["Sessions"],
        summary: "Récupération des sessions actives",
        description: "Récupère la liste des sessions actives de l'utilisateur",
        protect: true,
      },
    })
    .input(getActiveSessionsSchema())
    .output(getActiveSessionsResponseSchema())
    .query(getActiveSessions),
  deleteSession: authenticatedNoEmailVerificationProcedure
    .meta({
      openapi: {
        method: "DELETE",
        path: "/api/rest/me/sessions/{id}",
        tags: ["Sessions"],
        summary: "Suppression d'une session",
        description: "Supprime une session spécifique de l'utilisateur",
        protect: true,
      },
    })
    .input(deleteSessionSchema())
    .output(deleteSessionResponseSchema())
    .mutation(deleteSession),
  getAccount: authenticatedNoEmailVerificationProcedure
    .meta({
      openapi: {
        method: "GET",
        path: "/api/rest/me/account",
        tags: ["Profil utilisateur"],
        summary: "Récupération du compte utilisateur",
        description: "Récupère les informations complètes du compte de l'utilisateur connecté",
        protect: true,
      },
    })
    .input(z.void())
    .output(getAccountResponseSchema())
    .query(getAccount),
  deleteAccount: authenticatedNoEmailVerificationProcedure
    .meta({
      openapi: {
        method: "DELETE",
        path: "/api/rest/me/account",
        tags: ["Profil utilisateur"],
        summary: "Suppression du compte utilisateur",
        description: "Supprime définitivement le compte de l'utilisateur connecté",
        protect: true,
      },
    })
    .input(z.void())
    .output(deleteAccountResponseSchema())
    .mutation(deleteAccount),
  forgotPassword: publicProcedure
    .meta({
      openapi: {
        method: "POST",
        path: "/api/rest/me/forgot-password",
        tags: ["Authentification"],
        summary: "Demande de réinitialisation de mot de passe",
        description: "Envoie un email de réinitialisation de mot de passe à l'utilisateur",
        protect: false,
      },
    })
    .input(forgotPasswordSchema())
    .output(forgotPasswordResponseSchema())
    .mutation(forgotPassword),
  resetPassword: publicProcedure
    .meta({
      openapi: {
        method: "POST",
        path: "/api/rest/me/reset-password",
        tags: ["Authentification"],
        summary: "Réinitialisation du mot de passe",
        description: "Réinitialise le mot de passe de l'utilisateur avec un token valide",
        protect: false,
      },
    })
    .input(resetPasswordSchema())
    .output(resetPasswordResponseSchema())
    .mutation(resetPassword),
  sendVerificationEmail: authenticatedNoEmailVerificationProcedure
    .input(sendVerificationEmailSchema())
    .output(sendVerificationEmailResponseSchema())
    .mutation(sendVerificationEmail),
  verifyEmail: publicProcedure
    .meta({
      openapi: {
        method: "POST",
        path: "/api/rest/me/verify-email",
        tags: ["Vérification email"],
        summary: "Vérification de l'adresse email",
        description: "Vérifie l'adresse email de l'utilisateur avec un token valide",
        protect: false,
      },
    })
    .input(verifyEmailSchema())
    .output(verifyEmailResponseSchema())
    .mutation(verifyEmail),
})
