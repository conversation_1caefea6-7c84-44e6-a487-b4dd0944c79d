import { z } from "zod"

import { authenticatedProcedure, publicProcedure, router } from "@/lib/server/trpc"

/**
 * Test Router - Comprehensive test scenarios for tRPC-OpenAPI bridge
 *
 * This router contains test endpoints that cover all possible scenarios
 * for the tRPC-OpenAPI bridge implementation, including:
 * - Public vs Protected endpoints
 * - Different HTTP methods
 * - Various parameter types
 * - Mobile vs Web filtering
 * - Error handling scenarios
 */

// Test schemas
const testQuerySchema = z.object({
  message: z.string().describe("Test message parameter"),
  count: z.number().optional().describe("Optional count parameter"),
})

const testPathParamSchema = z.object({
  id: z.string().describe("Resource ID"),
  category: z.string().optional().describe("Optional category"),
})

const testBodySchema = z.object({
  title: z.string().min(1).max(100).describe("Test title"),
  description: z.string().optional().describe("Optional description"),
  tags: z.array(z.string()).optional().describe("Optional tags array"),
  metadata: z.record(z.string()).optional().describe("Optional metadata object"),
})

const testResponseSchema = z.object({
  success: z.boolean().describe("Operation success status"),
  data: z.any().optional().describe("Response data"),
  timestamp: z.string().describe("Response timestamp"),
})

export const testRouter = router({
  // Public GET endpoint with query parameters (Mobile compatible)
  publicGet: publicProcedure
    .meta({
      openapi: {
        method: "GET",
        path: "/api/rest/test/public",
        tags: ["Test"],
        summary: "Public GET endpoint test",
        description: "Test endpoint for public GET requests with query parameters",
        protect: false,
      },
      isMobile: true,
    })
    .input(testQuerySchema)
    .output(testResponseSchema)
    .query(({ input }) => ({
      success: true,
      data: { message: input.message, count: input.count || 0 },
      timestamp: new Date().toISOString(),
    })),

  // Public POST endpoint with request body (Mobile compatible)
  publicPost: publicProcedure
    .meta({
      openapi: {
        method: "POST",
        path: "/api/rest/test/public",
        tags: ["Test"],
        summary: "Public POST endpoint test",
        description: "Test endpoint for public POST requests with request body",
        protect: false,
      },
      isMobile: true,
    })
    .input(testBodySchema)
    .output(testResponseSchema)
    .mutation(({ input }) => ({
      success: true,
      data: input,
      timestamp: new Date().toISOString(),
    })),

  // Protected GET endpoint with path parameters (Mobile compatible)
  protectedGet: authenticatedProcedure
    .meta({
      openapi: {
        method: "GET",
        path: "/api/rest/test/protected/{id}",
        tags: ["Test"],
        summary: "Protected GET endpoint test",
        description: "Test endpoint for protected GET requests with path parameters",
        protect: true,
      },
      isMobile: true,
    })
    .input(testPathParamSchema)
    .output(testResponseSchema)
    .query(({ input, ctx }) => ({
      success: true,
      data: {
        id: input.id,
        category: input.category,
        userId: ctx.session?.user?.id,
      },
      timestamp: new Date().toISOString(),
    })),

  // Protected PUT endpoint (Mobile compatible)
  protectedPut: authenticatedProcedure
    .meta({
      openapi: {
        method: "PUT",
        path: "/api/rest/test/protected/{id}",
        tags: ["Test"],
        summary: "Protected PUT endpoint test",
        description: "Test endpoint for protected PUT requests",
        protect: true,
      },
      isMobile: true,
    })
    .input(
      z.object({
        id: z.string().describe("Resource ID"),
        ...testBodySchema.shape,
      })
    )
    .output(testResponseSchema)
    .mutation(({ input, ctx }) => ({
      success: true,
      data: {
        ...input,
        id: input.id,
        updated: true,
        userId: ctx.session?.user?.id,
      },
      timestamp: new Date().toISOString(),
    })),

  // Protected DELETE endpoint (Mobile compatible)
  protectedDelete: authenticatedProcedure
    .meta({
      openapi: {
        method: "DELETE",
        path: "/api/rest/test/protected/{id}",
        tags: ["Test"],
        summary: "Protected DELETE endpoint test",
        description: "Test endpoint for protected DELETE requests",
        protect: true,
      },
      isMobile: true,
    })
    .input(
      z.object({
        id: z.string().describe("Resource ID"),
      })
    )
    .output(testResponseSchema)
    .mutation(({ input, ctx }) => ({
      success: true,
      data: {
        id: input.id,
        deleted: true,
        userId: ctx.session?.user?.id,
      },
      timestamp: new Date().toISOString(),
    })),

  // Web-only endpoint (excluded from mobile)
  webOnly: publicProcedure
    .meta({
      openapi: {
        method: "GET",
        path: "/api/rest/test/web-only",
        tags: ["Test"],
        summary: "Web-only endpoint test",
        description: "Test endpoint that should only appear in web documentation",
        protect: false,
      },
      isMobile: false, // Exclude from mobile SDKs
    })
    .input(
      z.object({
        webFeature: z.string().describe("Web-specific feature parameter"),
      })
    )
    .output(testResponseSchema)
    .query(({ input }) => ({
      success: true,
      data: { webFeature: input.webFeature, platform: "web" },
      timestamp: new Date().toISOString(),
    })),

  // Error handling test endpoint
  errorTest: publicProcedure
    .meta({
      openapi: {
        method: "POST",
        path: "/api/rest/test/error",
        tags: ["Test"],
        summary: "Error handling test",
        description: "Test endpoint for error handling scenarios",
        protect: false,
      },
      isMobile: true,
    })
    .input(
      z.object({
        errorType: z.enum(["validation", "server", "not_found"]).describe("Type of error to simulate"),
      })
    )
    .output(testResponseSchema)
    .mutation(({ input }) => {
      switch (input.errorType) {
        case "validation":
          throw new Error("Validation error test")
        case "server":
          throw new Error("Internal server error test")
        case "not_found":
          throw new Error("Resource not found test")
        default:
          return {
            success: true,
            data: { errorType: input.errorType },
            timestamp: new Date().toISOString(),
          }
      }
    }),

  // Complex parameter types test
  complexParams: publicProcedure
    .meta({
      openapi: {
        method: "POST",
        path: "/api/rest/test/complex",
        tags: ["Test"],
        summary: "Complex parameter types test",
        description: "Test endpoint with various parameter types and validation",
        protect: false,
      },
      isMobile: true,
    })
    .input(
      z.object({
        stringParam: z.string().min(1).max(50),
        numberParam: z.number().min(0).max(100),
        booleanParam: z.boolean(),
        dateParam: z.string().datetime(),
        enumParam: z.enum(["option1", "option2", "option3"]),
        arrayParam: z.array(z.string()).min(1).max(5),
        objectParam: z.object({
          nested: z.string(),
          value: z.number(),
        }),
        optionalParam: z.string().optional(),
      })
    )
    .output(testResponseSchema)
    .mutation(({ input }) => ({
      success: true,
      data: input,
      timestamp: new Date().toISOString(),
    })),
})
