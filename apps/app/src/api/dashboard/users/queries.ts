import { z } from "zod"

import { prisma } from "@/lib/prisma"
import { ITrpcContext } from "@/types"
import { UserRole } from "@prisma/client"
import { TRPCError } from "@trpc/server"

import { logError, logExecutionTime, logQuery } from "../utilities"

import { getUsersListSchema } from "./schemas"

export async function getUsersList({
  input,
  ctx,
}: {
  input: z.infer<ReturnType<typeof getUsersListSchema>>
  ctx: ITrpcContext
}) {
  const startTime = Date.now()

  // Check if user is admin
  if (ctx.session?.user.role !== UserRole.ADMIN) {
    throw new TRPCError({
      code: "FORBIDDE<PERSON>",
      message: "Access denied. Admin role required.",
    })
  }

  const { page, limit, search, role, sortBy, sortOrder, isActive } = input
  logQuery("getUsersList", { page, limit, search, role, sortBy, sortOrder, isActive })

  const skip = (page - 1) * limit

  try {
    const where: Record<string, unknown> = {}

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { email: { contains: search, mode: "insensitive" } },
        { username: { contains: search, mode: "insensitive" } },
      ]
    }

    if (role) {
      where.role = role
    }

    if (isActive !== undefined) {
      // This is a simplified approach - we might want implement a proper "active" field later
      where.updatedAt = isActive
        ? { gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
        : { lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
    }

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        skip,
        take: limit,
        orderBy: { [sortBy]: sortOrder },
        include: {
          profilePicture: {
            select: {
              id: true,
              key: true,
              endpoint: true,
            },
          },
          athleteProfile: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              isAvailable: true,
            },
          },
          recruiterProfile: {
            select: {
              id: true,
              companyName: true,
              jobTitle: true,
            },
          },
          _count: {
            select: {
              posts: true,
              applications: true,
              sentInvitations: true,
            },
          },
        },
      }),
      prisma.user.count({ where }),
    ])

    const totalPages = Math.ceil(total / limit)

    logExecutionTime("getUsersList", startTime)

    return {
      users,
      pagination: {
        page,
        limit,
        total,
        totalPages,
      },
    }
  } catch (error) {
    logError("getUsersList", error, { page, limit, search, role, sortBy, sortOrder, isActive })
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Failed to fetch users list",
      cause: error,
    })
  }
}
