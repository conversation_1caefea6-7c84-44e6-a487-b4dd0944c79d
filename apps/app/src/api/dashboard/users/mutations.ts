import { z } from "zod"

import { prisma } from "@/lib/prisma"
import { ITrpcContext } from "@/types"
import { UserRole } from "@prisma/client"
import { TRPCError } from "@trpc/server"

import { logError, logExecutionTime, logQuery } from "../utilities"

import { updateUserStatusSchema } from "./schemas"

export async function updateUserStatus({
  input,
  ctx,
}: {
  input: z.infer<ReturnType<typeof updateUserStatusSchema>>
  ctx: ITrpcContext
}) {
  const startTime = Date.now()

  // Check if user is admin
  if (ctx.session?.user.role !== UserRole.ADMIN) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: "Access denied. Admin role required.",
    })
  }

  const { userId, isActive } = input
  logQuery("updateUserStatus", { userId, isActive })

  try {
    // For now, we'll just update the updatedAt field to simulate status change
    // In a real implementation, you might want to add an "isActive" field to the User model
    await prisma.user.update({
      where: { id: userId },
      data: {
        updatedAt: isActive ? new Date() : new Date(0), // Simplified approach
      },
    })

    logExecutionTime("updateUserStatus", startTime)

    return {
      success: true,
      message: `User status updated to ${isActive ? "active" : "inactive"}`,
    }
  } catch (error) {
    logError("updateUserStatus", error, { userId, isActive })
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Failed to update user status",
      cause: error,
    })
  }
}
