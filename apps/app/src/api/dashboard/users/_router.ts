import { authenticatedProcedure, router } from "@/lib/server/trpc"

import { updateUserStatus } from "./mutations"
import { getUsersList } from "./queries"
import {
  getUsersListResponseSchema,
  getUsersListSchema,
  updateUserStatusResponseSchema,
  updateUserStatusSchema,
} from "./schemas"

export const dashboardUserRouter = router({
  // User Management
  getUsersList: authenticatedProcedure
    // .meta({
    //   openapi: {
    //     method: "GET",
    //     path: "/api/rest/dashboard/users",
    //     tags: ["Dashboard", "Users"],
    //     summary: "Liste des utilisateurs",
    //     description: "Récupère la liste paginée des utilisateurs avec filtres et tri",
    //     protect: true,
    //   },
    // })
    .input(getUsersListSchema())
    .output(getUsersListResponseSchema())
    .query(getUsersList),

  updateUserStatus: authenticatedProcedure
    // .meta({
    //   openapi: {
    //     method: "PUT",
    //     path: "/api/rest/dashboard/users/{id}/status",
    //     tags: ["Dashboard", "Users"],
    //     summary: "Mise à jour du statut utilisateur",
    //     description: "Met à jour le statut d'un utilisateur (actif/inactif)",
    //     protect: true,
    //   },
    // })
    .input(updateUserStatusSchema())
    .output(updateUserStatusResponseSchema())
    .mutation(updateUserStatus),
})
