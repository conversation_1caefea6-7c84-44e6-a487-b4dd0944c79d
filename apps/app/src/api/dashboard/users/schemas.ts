import { z } from "zod"

import { UserRole } from "@prisma/client"

export const updateUserStatusSchema = () =>
  z.object({
    userId: z.string(),
    isActive: z.boolean(),
  })

export const updateUserStatusResponseSchema = () =>
  z.object({
    success: z.boolean(),
    message: z.string(),
  })
export const getUsersListSchema = () =>
  z.object({
    page: z.number().min(1).optional().default(1),
    limit: z.number().min(1).max(100).optional().default(20),
    search: z.string().optional(),
    role: z.nativeEnum(UserRole).optional(),
    sortBy: z.enum(["createdAt", "name", "email", "role"]).optional().default("createdAt"),
    sortOrder: z.enum(["asc", "desc"]).optional().default("desc"),
    isActive: z.boolean().optional(),
  })

export const getUsersListResponseSchema = () =>
  z.object({
    users: z.array(
      z.object({
        id: z.string(),
        name: z.string().nullable(),
        email: z.string().nullable(),
        username: z.string().nullable(),
        role: z.nativeEnum(UserRole),
        emailVerified: z.date().nullable(),
        phoneVerified: z.date().nullable(),
        createdAt: z.date(),
        updatedAt: z.date(),
        profilePicture: z
          .object({
            id: z.string(),
            key: z.string(),
            endpoint: z.string(),
          })
          .nullable(),
        athleteProfile: z
          .object({
            id: z.string(),
            firstName: z.string(),
            lastName: z.string(),
            isAvailable: z.boolean(),
          })
          .nullable(),
        recruiterProfile: z
          .object({
            id: z.string(),
            companyName: z.string().nullable(),
            jobTitle: z.string().nullable(),
          })
          .nullable(),
        _count: z.object({
          posts: z.number(),
          applications: z.number(),
          sentInvitations: z.number(),
        }),
      })
    ),
    pagination: z.object({
      page: z.number(),
      limit: z.number(),
      total: z.number(),
      totalPages: z.number(),
    }),
  })
