import { logger } from "@proamlink/lib"

// Logging utility for dashboard queries
export const logQuery = (queryName: string, params: Record<string, unknown>) => {
  logger.log(`[Dashboard Query] ${queryName}:`, {
    timestamp: new Date().toISOString(),
    params,
  })
}

export const logError = (queryName: string, error: unknown, context?: Record<string, unknown>) => {
  logger.error(`[Dashboard Query Error] ${queryName}:`, {
    timestamp: new Date().toISOString(),
    error: error instanceof Error ? error.message : String(error),
    stack: error instanceof Error ? error.stack : undefined,
    context,
  })
}

export const logExecutionTime = (queryName: string, startTime: number) => {
  const executionTime = Date.now() - startTime
  logger.log(`[Dashboard Query Performance] ${queryName}: ${executionTime}ms`)
}
