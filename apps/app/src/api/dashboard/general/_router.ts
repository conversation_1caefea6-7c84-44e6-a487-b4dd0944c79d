import { authenticatedProcedure, router } from "@/lib/server/trpc"

import { getApplicationAnalytics, getDashboardKPIs, getUserAnalytics } from "./queries"
import {
  getApplicationAnalyticsResponseSchema,
  getApplicationAnalyticsSchema,
  getDashboardKPIsResponseSchema,
  getDashboardKPIsSchema,
  getUserAnalyticsResponseSchema,
  getUserAnalyticsSchema,
} from "./schemas"

export const dashboardGeneralRouter = router({
  // KPI Metrics
  getKPIs: authenticatedProcedure
    .meta({
      openapi: {
        method: "GET",
        path: "/api/rest/dashboard/kpis",
        tags: ["Dashboard"],
        summary: "Récupération des KPIs du tableau de bord",
        description: "Récupère les indicateurs clés de performance pour le tableau de bord administrateur",
        protect: true,
      },
    })
    .input(getDashboardKPIsSchema())
    .output(getDashboardKPIsResponseSchema())
    .query(getDashboardKPIs),

  // Analytics
  getUserAnalytics: authenticatedProcedure
    .meta({
      openapi: {
        method: "GET",
        path: "/api/rest/dashboard/analytics/users",
        tags: ["Dashboard", "Analytics"],
        summary: "Analytiques des utilisateurs",
        description: "Récupère les données analytiques concernant les utilisateurs",
        protect: true,
      },
    })
    .input(getUserAnalyticsSchema())
    .output(getUserAnalyticsResponseSchema())
    .query(getUserAnalytics),

  getApplicationAnalytics: authenticatedProcedure
    .meta({
      openapi: {
        method: "GET",
        path: "/api/rest/dashboard/analytics/applications",
        tags: ["Dashboard", "Analytics"],
        summary: "Analytiques des candidatures",
        description: "Récupère les données analytiques concernant les candidatures",
        protect: true,
      },
    })
    .input(getApplicationAnalyticsSchema())
    .output(getApplicationAnalyticsResponseSchema())
    .query(getApplicationAnalytics),
})
