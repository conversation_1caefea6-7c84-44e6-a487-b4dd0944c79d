import { z } from "zod"

import { prisma } from "@/lib/prisma"
import { ITrpcContext } from "@/types"
import { UserRole } from "@prisma/client"
import { TRPCError } from "@trpc/server"

import { logError, logExecutionTime, logQuery } from "../utilities"

import { getApplicationAnalyticsSchema, getDashboardKPIsSchema, getUserAnalyticsSchema } from "./schemas"

export async function getDashboardKPIs({
  input,
  ctx,
}: {
  input: z.infer<ReturnType<typeof getDashboardKPIsSchema>>
  ctx: ITrpcContext
}) {
  const startTime = Date.now()

  // Check if user is admin
  if (ctx.session?.user.role !== UserRole.ADMIN) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: "Access denied. Admin role required.",
    })
  }

  const { period } = input
  logQuery("getDashboardKPIs", { period })

  const now = new Date()
  const periodStart = new Date()

  switch (period) {
    case "7d":
      periodStart.setDate(now.getDate() - 7)
      break
    case "30d":
      periodStart.setDate(now.getDate() - 30)
      break
    case "90d":
      periodStart.setDate(now.getDate() - 90)
      break
    case "1y":
      periodStart.setFullYear(now.getFullYear() - 1)
      break
  }

  try {
    // Get total counts
    const [
      totalUsers,
      totalAthletes,
      totalRecruiters,
      totalJobOffers,
      totalApplications,
      totalPosts,
      newUsersThisPeriod,
      applicationsThisPeriod,
      jobOffersThisPeriod,
      activeUsers,
    ] = await Promise.all([
      prisma.user.count(),
      prisma.athleteProfile.count(),
      prisma.recruiterProfile.count(),
      prisma.jobOffer.count(),
      prisma.application.count(),
      prisma.post.count(),
      prisma.user.count({
        where: {
          createdAt: {
            gte: periodStart,
          },
        },
      }),
      prisma.application.count({
        where: {
          appliedAt: {
            gte: periodStart,
          },
        },
      }),
      prisma.jobOffer.count({
        where: {
          createdAt: {
            gte: periodStart,
          },
        },
      }),
      prisma.user.count({
        where: {
          updatedAt: {
            gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
          },
        },
      }),
    ])

    // Calculate growth rate
    const previousPeriodStart = new Date(periodStart)
    previousPeriodStart.setTime(periodStart.getTime() - (now.getTime() - periodStart.getTime()))

    const usersInPreviousPeriod = await prisma.user.count({
      where: {
        createdAt: {
          gte: previousPeriodStart,
          lt: periodStart,
        },
      },
    })

    const userGrowthRate =
      usersInPreviousPeriod > 0 ? ((newUsersThisPeriod - usersInPreviousPeriod) / usersInPreviousPeriod) * 100 : 0

    // Calculate engagement rate (simplified: users with posts/likes in period)
    const engagedUsers = await prisma.user.count({
      where: {
        OR: [
          {
            posts: {
              some: {
                createdAt: {
                  gte: periodStart,
                },
              },
            },
          },
          {
            likes: {
              some: {
                createdAt: {
                  gte: periodStart,
                },
              },
            },
          },
        ],
      },
    })

    const engagementRate = totalUsers > 0 ? (engagedUsers / totalUsers) * 100 : 0

    logExecutionTime("getDashboardKPIs", startTime)

    return {
      totalUsers,
      totalAthletes,
      totalRecruiters,
      totalJobOffers,
      totalApplications,
      totalPosts,
      activeUsers,
      newUsersThisPeriod,
      applicationsThisPeriod,
      jobOffersThisPeriod,
      engagementRate: Math.round(engagementRate * 100) / 100,
      userGrowthRate: Math.round(userGrowthRate * 100) / 100,
    }
  } catch (error) {
    logError("getDashboardKPIs", error, { period, periodStart })
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Failed to fetch dashboard KPIs",
      cause: error,
    })
  }
}

export async function getUserAnalytics({
  input,
  ctx,
}: {
  input: z.infer<ReturnType<typeof getUserAnalyticsSchema>>
  ctx: ITrpcContext
}) {
  const startTime = Date.now()

  // Check if user is admin
  if (ctx.session?.user.role !== UserRole.ADMIN) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: "Access denied. Admin role required.",
    })
  }

  const { period, granularity } = input
  logQuery("getUserAnalytics", { period, granularity })

  const now = new Date()
  const periodStart = new Date()

  switch (period) {
    case "7d":
      periodStart.setDate(now.getDate() - 7)
      break
    case "30d":
      periodStart.setDate(now.getDate() - 30)
      break
    case "90d":
      periodStart.setDate(now.getDate() - 90)
      break
    case "1y":
      periodStart.setFullYear(now.getFullYear() - 1)
      break
  }

  try {
    // Get user registrations over time
    // Use string interpolation for granularity since it's a safe enum value
    const sqlQuery = `
      SELECT
        DATE_TRUNC('${granularity}', "createdAt") as date,
        COUNT(*) as count,
        COUNT(CASE WHEN role = 'ATHLETE' THEN 1 END) as athletes,
        COUNT(CASE WHEN role = 'RECRUITER' THEN 1 END) as recruiters,
        COUNT(CASE WHEN role = 'ADMIN' THEN 1 END) as admins
      FROM "User"
      WHERE "createdAt" >= $1
      GROUP BY DATE_TRUNC('${granularity}', "createdAt")
      ORDER BY date ASC
    `

    const userRegistrationsByDate = await prisma.$queryRawUnsafe<
      Array<{
        date: string
        count: bigint
        athletes: bigint
        recruiters: bigint
        admins: bigint
      }>
    >(sqlQuery, periodStart)

    // Get users by role
    const usersByRole = await prisma.user.groupBy({
      by: ["role"],
      _count: {
        role: true,
      },
    })

    // Get top countries (simplified - using city field)
    const topCountries = await prisma.athleteProfile.groupBy({
      by: ["country"],
      _count: {
        country: true,
      },
      where: {
        country: {
          not: null,
        },
      },
      orderBy: {
        _count: {
          country: "desc",
        },
      },
      take: 10,
    })

    // Get verification stats
    const [emailVerified, phoneVerified, total] = await Promise.all([
      prisma.user.count({
        where: {
          emailVerified: {
            not: null,
          },
        },
      }),
      prisma.user.count({
        where: {
          phoneVerified: {
            not: null,
          },
        },
      }),
      prisma.user.count(),
    ])

    logExecutionTime("getUserAnalytics", startTime)

    return {
      userRegistrations: userRegistrationsByDate.map((row) => ({
        date: typeof row.date === "string" ? row.date : new Date(row.date).toISOString(),
        count: Number(row.count),
        athletes: Number(row.athletes),
        recruiters: Number(row.recruiters),
        admins: Number(row.admins),
      })),
      usersByRole: {
        athletes: usersByRole.find((r) => r.role === UserRole.ATHLETE)?._count.role || 0,
        recruiters: usersByRole.find((r) => r.role === UserRole.RECRUITER)?._count.role || 0,
        admins: usersByRole.find((r) => r.role === UserRole.ADMIN)?._count.role || 0,
      },
      topCountries: topCountries.map((country) => ({
        country: country.country || "Unknown",
        count: country._count.country,
      })),
      verificationStats: {
        emailVerified,
        phoneVerified,
        unverified: total - emailVerified,
      },
    }
  } catch (error) {
    logError("getUserAnalytics", error, { period, granularity, periodStart })
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Failed to fetch user analytics",
      cause: error,
    })
  }
}

export async function getApplicationAnalytics({
  input,
  ctx,
}: {
  input: z.infer<ReturnType<typeof getApplicationAnalyticsSchema>>
  ctx: ITrpcContext
}) {
  const startTime = Date.now()

  // Check if user is admin
  if (ctx.session?.user.role !== UserRole.ADMIN) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: "Access denied. Admin role required.",
    })
  }

  const { period, granularity } = input
  logQuery("getApplicationAnalytics", { period, granularity })

  const now = new Date()
  const periodStart = new Date()

  switch (period) {
    case "7d":
      periodStart.setDate(now.getDate() - 7)
      break
    case "30d":
      periodStart.setDate(now.getDate() - 30)
      break
    case "90d":
      periodStart.setDate(now.getDate() - 90)
      break
    case "1y":
      periodStart.setFullYear(now.getFullYear() - 1)
      break
  }

  try {
    // Get application trends over time using separate queries and combine in TypeScript
    // This approach avoids complex SQL GROUP BY issues

    // Get applications by date
    const applicationsSql = `
      SELECT
        DATE_TRUNC('${granularity}', "appliedAt") as date,
        COUNT(*) as count
      FROM "Application"
      WHERE "appliedAt" >= $1
      GROUP BY DATE_TRUNC('${granularity}', "appliedAt")
      ORDER BY date ASC
    `
    const applicationsByDate = await prisma.$queryRawUnsafe<
      Array<{
        date: string
        count: bigint
      }>
    >(applicationsSql, periodStart)

    // Get job offers by date
    const jobOffersSql = `
      SELECT
        DATE_TRUNC('${granularity}', "createdAt") as date,
        COUNT(*) as count
      FROM "JobOffer"
      WHERE "createdAt" >= $1
      GROUP BY DATE_TRUNC('${granularity}', "createdAt")
      ORDER BY date ASC
    `
    const jobOffersByDate = await prisma.$queryRawUnsafe<
      Array<{
        date: string
        count: bigint
      }>
    >(jobOffersSql, periodStart)

    // Get invitations by date
    const invitationsSql = `
      SELECT
        DATE_TRUNC('${granularity}', "sentAt") as date,
        COUNT(*) as count
      FROM "Invitation"
      WHERE "sentAt" >= $1
      GROUP BY DATE_TRUNC('${granularity}', "sentAt")
      ORDER BY date ASC
    `
    const invitationsByDate = await prisma.$queryRawUnsafe<
      Array<{
        date: string
        count: bigint
      }>
    >(invitationsSql, periodStart)

    // Combine the results in TypeScript
    const dateMap = new Map<string, { applications: number; jobOffers: number; invitations: number }>()

    // Process applications
    applicationsByDate.forEach((row) => {
      const date = row.date
      if (!dateMap.has(date)) {
        dateMap.set(date, { applications: 0, jobOffers: 0, invitations: 0 })
      }
      dateMap.get(date)!.applications = Number(row.count)
    })

    // Process job offers
    jobOffersByDate.forEach((row) => {
      const date = row.date
      if (!dateMap.has(date)) {
        dateMap.set(date, { applications: 0, jobOffers: 0, invitations: 0 })
      }
      dateMap.get(date)!.jobOffers = Number(row.count)
    })

    // Process invitations
    invitationsByDate.forEach((row) => {
      const date = row.date
      if (!dateMap.has(date)) {
        dateMap.set(date, { applications: 0, jobOffers: 0, invitations: 0 })
      }
      dateMap.get(date)!.invitations = Number(row.count)
    })

    // Convert to array and sort by date
    const applicationTrends = Array.from(dateMap.entries())
      .map(([date, data]) => ({
        date,
        applications: data.applications,
        jobOffers: data.jobOffers,
        invitations: data.invitations,
      }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())

    // Get applications by status
    const applicationsByStatus = await prisma.application.groupBy({
      by: ["status"],
      _count: {
        status: true,
      },
    })

    // Get top sports
    const topSports = await prisma.sport.findMany({
      include: {
        jobOffers: {
          select: {
            id: true,
            applications: {
              select: {
                id: true,
              },
            },
          },
        },
      },
      orderBy: {
        jobOffers: {
          _count: "desc",
        },
      },
      take: 10,
    })

    // Calculate conversion rates
    const [totalApplications, reviewedApplications, acceptedApplications] = await Promise.all([
      prisma.application.count(),
      prisma.application.count({
        where: {
          status: {
            in: ["REVIEWED", "ACCEPTED", "REJECTED"],
          },
        },
      }),
      prisma.application.count({
        where: {
          status: "ACCEPTED",
        },
      }),
    ])

    const applicationToReview = totalApplications > 0 ? (reviewedApplications / totalApplications) * 100 : 0
    const reviewToAcceptance = reviewedApplications > 0 ? (acceptedApplications / reviewedApplications) * 100 : 0
    const overallSuccess = totalApplications > 0 ? (acceptedApplications / totalApplications) * 100 : 0

    logExecutionTime("getApplicationAnalytics", startTime)

    return {
      applicationTrends: applicationTrends.map((row) => ({
        date: typeof row.date === "string" ? row.date : new Date(row.date).toISOString(),
        applications: row.applications,
        jobOffers: row.jobOffers,
        invitations: row.invitations,
      })),
      applicationsByStatus: {
        pending: applicationsByStatus.find((s) => s.status === "PENDING")?._count.status || 0,
        reviewed: applicationsByStatus.find((s) => s.status === "REVIEWED")?._count.status || 0,
        accepted: applicationsByStatus.find((s) => s.status === "ACCEPTED")?._count.status || 0,
        rejected: applicationsByStatus.find((s) => s.status === "REJECTED")?._count.status || 0,
        withdrawn: applicationsByStatus.find((s) => s.status === "WITHDRAWN")?._count.status || 0,
      },
      topSports: topSports.map((sport) => ({
        sport: sport.name,
        applications: sport.jobOffers.reduce((acc, job) => acc + job.applications.length, 0),
        jobOffers: sport.jobOffers.length,
      })),
      conversionRates: {
        applicationToReview: Math.round(applicationToReview * 100) / 100,
        reviewToAcceptance: Math.round(reviewToAcceptance * 100) / 100,
        overallSuccess: Math.round(overallSuccess * 100) / 100,
      },
    }
  } catch (error) {
    logError("getApplicationAnalytics", error, { period, granularity, periodStart })
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Failed to fetch application analytics",
      cause: error,
    })
  }
}
