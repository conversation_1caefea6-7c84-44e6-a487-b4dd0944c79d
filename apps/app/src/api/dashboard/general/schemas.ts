import { z } from "zod"

export const getDashboardKPIsSchema = () =>
  z.object({
    period: z.enum(["7d", "30d", "90d", "1y"]).optional().default("30d"),
  })

export const getDashboardKPIsResponseSchema = () =>
  z.object({
    totalUsers: z.number(),
    totalAthletes: z.number(),
    totalRecruiters: z.number(),
    totalJobOffers: z.number(),
    totalApplications: z.number(),
    totalPosts: z.number(),
    activeUsers: z.number(),
    newUsersThisPeriod: z.number(),
    applicationsThisPeriod: z.number(),
    jobOffersThisPeriod: z.number(),
    engagementRate: z.number(),
    userGrowthRate: z.number(),
  })

// Analytics Schemas
export const getUserAnalyticsSchema = () =>
  z.object({
    period: z.enum(["7d", "30d", "90d", "1y"]).optional().default("30d"),
    granularity: z.enum(["day", "week", "month"]).optional().default("day"),
  })

export const getUserAnalyticsResponseSchema = () =>
  z.object({
    userRegistrations: z.array(
      z.object({
        date: z.string(),
        count: z.number(),
        athletes: z.number(),
        recruiters: z.number(),
        admins: z.number(),
      })
    ),
    usersByRole: z.object({
      athletes: z.number(),
      recruiters: z.number(),
      admins: z.number(),
    }),
    topCountries: z.array(
      z.object({
        country: z.string(),
        count: z.number(),
      })
    ),
    verificationStats: z.object({
      emailVerified: z.number(),
      phoneVerified: z.number(),
      unverified: z.number(),
    }),
  })

export const getApplicationAnalyticsSchema = () =>
  z.object({
    period: z.enum(["7d", "30d", "90d", "1y"]).optional().default("30d"),
    granularity: z.enum(["day", "week", "month"]).optional().default("day"),
  })

export const getApplicationAnalyticsResponseSchema = () =>
  z.object({
    applicationTrends: z.array(
      z.object({
        date: z.string(),
        applications: z.number(),
        jobOffers: z.number(),
        invitations: z.number(),
      })
    ),
    applicationsByStatus: z.object({
      pending: z.number(),
      reviewed: z.number(),
      accepted: z.number(),
      rejected: z.number(),
      withdrawn: z.number(),
    }),
    topSports: z.array(
      z.object({
        sport: z.string(),
        applications: z.number(),
        jobOffers: z.number(),
      })
    ),
    conversionRates: z.object({
      applicationToReview: z.number(),
      reviewToAcceptance: z.number(),
      overallSuccess: z.number(),
    }),
  })
