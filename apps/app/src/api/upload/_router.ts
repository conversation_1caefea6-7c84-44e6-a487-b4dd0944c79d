import { authenticatedProcedure, router } from "@/lib/server/trpc"

import { presignedUrl } from "./mutations"
import { presignedUrlResponseSchema, presignedUrlSchema } from "./schemas"

export const uploadRouter = router({
  presignedUrl: authenticatedProcedure
    .meta({
      openapi: {
        method: "POST",
        path: "/api/rest/upload/presigned-url",
        tags: ["Upload"],
        summary: "Génération d'une URL pré-signée",
        description: "Génère une URL pré-signée pour l'upload direct de fichiers vers S3",
        protect: true,
      },
    })
    .input(presignedUrlSchema())
    .output(presignedUrlResponseSchema())
    .mutation(presignedUrl),
})
