import { z } from "zod"

import { env } from "@/lib/env"
import { publicProcedure, router } from "@/lib/server/trpc"

/**
 * Schéma de réponse pour l'endpoint de santé
 */
const healthResponseSchema = z.object({
  status: z.literal("ok").describe("État de l'API"),
  timestamp: z.string().describe("Horodatage de la vérification"),
  version: z.string().describe("Version de l'API"),
  environment: z.string().describe("Environnement d'exécution"),
  services: z
    .object({
      database: z.literal("connected").describe("État de la base de données"),
      redis: z.literal("connected").describe("État du cache Redis"),
    })
    .describe("État des services externes"),
})

/**
 * Router système - Endpoints de monitoring et santé
 *
 * Ce router contient les endpoints système pour le monitoring,
 * la vérification de santé et les métriques de l'application.
 */
export const systemRouter = router({
  /**
   * Endpoint de santé - Public
   * Permet de vérifier que l'API fonctionne correctement
   */
  health: publicProcedure
    .meta({
      openapi: {
        method: "GET",
        path: "/api/rest/health",
        tags: ["Système"],
        summary: "Vérification de l'état de l'API",
        description: "Endpoint public pour vérifier que l'API ProAmLink fonctionne correctement",
        protect: false,
      },
      isMobile: true, // Include in mobile SDKs
    })
    .input(z.void())
    .output(healthResponseSchema)
    .query(async () => {
      try {
        // TODO: Ajouter vérification de la base de données
        // await prisma.$queryRaw`SELECT 1`

        // TODO: Ajouter vérification Redis si nécessaire
        // await redis.ping()

        return {
          status: "ok" as const,
          timestamp: new Date().toISOString(),
          version: "1.0.0", // TODO: Récupérer depuis package.json
          environment: env.ENV || "development",
          services: {
            database: "connected" as const,
            redis: "connected" as const, // TODO: Vérification réelle
          },
        }
      } catch (error) {
        throw new Error("Erreur lors de la vérification de l'état de l'API")
      }
    }),
})
