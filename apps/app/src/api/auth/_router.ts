import { z } from "zod"

import {
  desactivateTotpResponseSchema,
  desactivateTotpSchema,
  generateTotpSecretResponseSchema,
  recover2FAResponseSchema,
  recover2FASchema,
  signUpSchema,
  verifyTotpResponseSchema,
  verifyTotpSchema,
} from "@/api/auth/schemas"
import { authenticatedProcedure, publicProcedure, router } from "@/lib/server/trpc"

import { signUpResponseSchema } from "../me/schemas"

import { desactivateTotp, generateTotpSecret, recover2FA, register, verifyTotp } from "./mutations"

export const authRouter = router({
  register: publicProcedure
    .meta({
      openapi: {
        method: "POST",
        path: "/api/rest/auth/register",
        tags: ["Authentification"],
        summary: "Inscription d'un nouvel utilisateur",
        description: "Crée un nouveau compte utilisateur avec les informations fournies",
        protect: false,
      },
    })
    .input(signUpSchema())
    .output(signUpResponseSchema())
    .mutation(register),
  generateTotpSecret: authenticatedProcedure
    .meta({
      openapi: {
        method: "POST",
        path: "/api/rest/auth/2fa/generate-secret",
        tags: ["Authentification 2FA"],
        summary: "Génération d'un secret TOTP",
        description: "Génère un nouveau secret TOTP pour l'authentification à deux facteurs",
        protect: true,
      },
    })
    .input(z.void())
    .output(generateTotpSecretResponseSchema())
    .mutation(generateTotpSecret),
  verifyTotp: authenticatedProcedure
    .meta({
      openapi: {
        method: "POST",
        path: "/api/rest/auth/2fa/verify",
        tags: ["Authentification 2FA"],
        summary: "Vérification du code TOTP",
        description: "Vérifie un code TOTP pour activer l'authentification à deux facteurs",
        protect: true,
      },
    })
    .input(verifyTotpSchema())
    .output(verifyTotpResponseSchema())
    .mutation(verifyTotp),
  desactivateTotp: authenticatedProcedure
    .meta({
      openapi: {
        method: "POST",
        path: "/api/rest/auth/2fa/deactivate",
        tags: ["Authentification 2FA"],
        summary: "Désactivation de l'authentification 2FA",
        description: "Désactive l'authentification à deux facteurs pour l'utilisateur connecté",
        protect: true,
      },
    })
    .input(desactivateTotpSchema())
    .output(desactivateTotpResponseSchema())
    .mutation(desactivateTotp),
  recover2FA: publicProcedure
    .meta({
      openapi: {
        method: "POST",
        path: "/api/rest/auth/2fa/recover",
        tags: ["Authentification 2FA"],
        summary: "Récupération d'accès 2FA",
        description: "Permet de récupérer l'accès au compte en cas de perte du dispositif 2FA",
        protect: false,
      },
    })
    .input(recover2FASchema())
    .output(recover2FAResponseSchema())
    .mutation(recover2FA),
})
