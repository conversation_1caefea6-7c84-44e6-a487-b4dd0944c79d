import { router } from "@/lib/server/trpc"

import { authRouter } from "./auth/_router"
import { meRouter } from "./me/_router"
import { systemRouter } from "./system/_router"
import { testRouter } from "./test/_router"
import { uploadRouter } from "./upload/_router"
import { dashboardGeneralRouter, dashboardUserRouter } from "./dashboard"

export const appRouter = router({
  auth: authRouter,
  dashboardGeneral: dashboardGeneralRouter,
  dashboardUser: dashboardUserRouter,
  me: meRouter,
  system: systemRouter,
  test: testRouter,
  upload: uploadRouter,
})

export type AppRouter = typeof appRouter
