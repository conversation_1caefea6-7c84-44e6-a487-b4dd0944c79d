{"dashboard": {"title": "Dashboard", "subtitle": "ProAmLink Admin Dashboard", "overview": {"title": "Overview", "subtitle": "Key performance indicators and metrics", "kpis": "Key Performance Indicators"}, "navigation": {"overview": "Overview", "users": "Users", "jobs": "Job Offers", "applications": "Applications", "analytics": "Analytics", "reports": "Reports", "moderation": "Moderation", "settings": "Settings"}, "breadcrumbs": {"dashboard": "Dashboard", "overview": "Overview", "users": "Users", "jobs": "Job Offers", "applications": "Applications", "analytics": "Analytics", "reports": "Reports", "moderation": "Moderation", "settings": "Settings"}, "kpis": {"totalUsers": "Total Users", "totalAthletes": "Athletes", "totalRecruiters": "Recruiters", "totalJobOffers": "Job Offers", "totalApplications": "Applications", "totalPosts": "Posts", "activeUsers": "Active Users", "engagementRate": "Engagement Rate", "newUsers": "New Users", "newApplications": "New Applications", "newJobOffers": "New Job Offers", "descriptions": {"athleteProfiles": "Athlete profiles", "recruiterProfiles": "Recruiter profiles", "postsCreated": "Posts created", "last30Days": "Last 30 days", "activeUsers": "Active users"}, "trends": {"vsPreviousPeriod": "vs previous period", "newThisPeriod": "new this period"}}, "periods": {"7d": "Last 7 days", "30d": "Last 30 days", "90d": "Last 90 days", "1y": "1 year"}, "granularity": {"day": "Daily", "week": "Weekly", "month": "Monthly"}, "charts": {"userGrowth": {"title": "User Growth", "description": "Evolution of user registrations by type"}, "userDistribution": {"title": "User Distribution", "description": "Distribution by user type"}, "applicationTrends": {"title": "Application Trends", "description": "Evolution of applications, job offers and invitations"}, "engagement": {"title": "User Engagement", "description": "User activity on the platform"}}, "users": {"title": "User Management", "subtitle": "Manage users, athletes and recruiters on the platform", "stats": {"total": "Total users", "showing": "Showing", "pages": "Pages"}, "filters": {"title": "Filters and actions", "search": "Search by name, email or username...", "filterByRole": "Filter by role", "filterByStatus": "Filter by status", "allRoles": "All roles", "allStatuses": "All", "active": "Active", "inactive": "Inactive"}, "table": {"columns": {"user": "USER", "role": "ROLE", "status": "STATUS", "verification": "VERIFICATION", "activity": "ACTIVITY", "createdAt": "CREATED DATE", "actions": "ACTIONS"}, "roles": {"admin": "Admin", "athlete": "Athlete", "recruiter": "Rec<PERSON>er"}, "status": {"active": "Active", "inactive": "Inactive"}, "verification": {"email": "Email", "phone": "Phone", "emailVerified": "Email verified", "phoneVerified": "Phone verified", "emailNotVerified": "Email not verified", "phoneNotVerified": "Phone not verified"}, "actions": {"viewProfile": "View profile", "edit": "Edit", "activate": "Activate", "deactivate": "Deactivate", "delete": "Delete"}, "activity": {"posts": "posts", "applications": "applications", "invitations": "invitations"}}, "actions": {"export": "Export", "add": "Add"}, "activeFilters": {"search": "Search", "role": "Role", "status": "Status"}}, "analytics": {"title": "Analytics", "subtitle": "Detailed analysis and performance metrics of the platform", "tabs": {"users": "Users", "applications": "Applications"}, "topCountries": {"title": "Top 10 countries", "title5": "Top countries"}, "verificationStats": {"title": "Verification Status", "emailVerified": "Email verified", "phoneVerified": "Phone verified"}, "topSports": {"title": "Most popular sports", "applications": "applications"}, "conversionRates": {"title": "Conversion Rates", "overallSuccess": "Overall success rate", "applicationToReview": "Application → Review", "reviewToAcceptance": "Review → Acceptance"}}, "common": {"loading": "Loading...", "noData": "No data available", "error": "An error occurred", "tryAgain": "Try again", "refresh": "Refresh", "close": "Close", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "search": "Search...", "notifications": "Notifications", "userMenu": "User menu", "profile": "My profile", "logout": "Sign out", "toggleMobileMenu": "Toggle mobile menu", "dashboardNavigation": "Dashboard navigation", "version": "Version 1.0.0", "adminDashboard": "Admin Dashboard"}}}