{"dashboard": {"title": "Tableau de bord", "subtitle": "Tableau de bord administrateur ProAmLink", "overview": {"title": "Vue d'ensemble", "subtitle": "Indicateurs clés de performance et métriques", "kpis": "Indicateurs clés de performance"}, "navigation": {"overview": "Vue d'ensemble", "users": "Utilisateurs", "jobs": "Offres d'emploi", "applications": "Candidatures", "analytics": "Analytiques", "reports": "Rapports", "moderation": "Modération", "settings": "Paramètres"}, "breadcrumbs": {"dashboard": "Tableau de bord", "overview": "Vue d'ensemble", "users": "Utilisateurs", "jobs": "Offres d'emploi", "applications": "Candidatures", "analytics": "Analytiques", "reports": "Rapports", "moderation": "Modération", "settings": "Paramètres"}, "kpis": {"totalUsers": "Total Utilisateurs", "totalAthletes": "<PERSON><PERSON><PERSON><PERSON>", "totalRecruiters": "Recruteurs", "totalJobOffers": "Offres d'emploi", "totalApplications": "Candidatures", "totalPosts": "Publications", "activeUsers": "Utilisateurs actifs", "engagementRate": "Taux d'engagement", "newUsers": "Nouveaux utilisateurs", "newApplications": "Nouvelles candidatures", "newJobOffers": "Nouvelles offres", "descriptions": {"athleteProfiles": "Profils athlètes", "recruiterProfiles": "Profils recruteurs", "postsCreated": "Posts créés", "last30Days": "30 derniers jours", "activeUsers": "Utilisateurs actifs"}, "trends": {"vsPreviousPeriod": "vs période précédente", "newThisPeriod": "nouvelles cette période"}}, "periods": {"7d": "7 derniers jours", "30d": "30 derniers jours", "90d": "90 derniers jours", "1y": "1 an"}, "granularity": {"day": "Par jour", "week": "Par semaine", "month": "Par mois"}, "charts": {"userGrowth": {"title": "Croissance des utilisateurs", "description": "Évolution du nombre d'inscriptions par type d'utilisateur"}, "userDistribution": {"title": "Répartition des utilisateurs", "description": "Distribution par type d'utilisateur"}, "applicationTrends": {"title": "Tendances des candidatures", "description": "Évolution des candidatures, offres d'emploi et invitations"}, "engagement": {"title": "Engagement des utilisateurs", "description": "Activité des utilisateurs sur la plateforme"}}, "users": {"title": "Gestion des utilisateurs", "subtitle": "<PERSON><PERSON><PERSON> les utilisateurs, athlètes et recruteurs de la plateforme", "stats": {"total": "Total utilisateurs", "showing": "Affichés", "pages": "Pages"}, "filters": {"title": "Filtres et actions", "search": "Rechercher par nom, email ou nom d'utilisateur...", "filterByRole": "Filtrer par rôle", "filterByStatus": "Filtrer par statut", "allRoles": "To<PERSON> les rôles", "allStatuses": "Tous", "active": "Actifs", "inactive": "Inactifs"}, "table": {"columns": {"user": "UTILISATEUR", "role": "RÔLE", "status": "STATUT", "verification": "VÉRIFICATION", "activity": "ACTIVITÉ", "createdAt": "DATE CRÉATION", "actions": "ACTIONS"}, "roles": {"admin": "Admin", "athlete": "Athlè<PERSON>", "recruiter": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "status": {"active": "Actif", "inactive": "Inactif"}, "verification": {"email": "Email", "phone": "<PERSON><PERSON><PERSON>", "emailVerified": "<PERSON>ail v<PERSON>", "phoneVerified": "Téléphone vérifié", "emailNotVerified": "Email non vérifié", "phoneNotVerified": "Téléphone non vérifié"}, "actions": {"viewProfile": "Voir le profil", "edit": "Modifier", "activate": "Activer", "deactivate": "Désactiver", "delete": "<PERSON><PERSON><PERSON><PERSON>"}, "activity": {"posts": "posts", "applications": "candidatures", "invitations": "invitations"}}, "actions": {"export": "Exporter", "add": "Ajouter"}, "activeFilters": {"search": "Recherche", "role": "R<PERSON><PERSON>", "status": "Statut"}}, "analytics": {"title": "Analytiques", "subtitle": "Analyses d<PERSON>lées et métriques de performance de la plateforme", "tabs": {"users": "Utilisateurs", "applications": "Candidatures"}, "topCountries": {"title": "Top 10 pays", "title5": "Top pays"}, "verificationStats": {"title": "Statut de vérification", "emailVerified": "<PERSON>ail v<PERSON>", "phoneVerified": "Téléphone vérifié"}, "topSports": {"title": "Sports les plus populaires", "applications": "candidatures"}, "conversionRates": {"title": "Taux de conversion", "overallSuccess": "Taux de succès global", "applicationToReview": "Candidature → Examen", "reviewToAcceptance": "Examen → Acceptation"}}, "common": {"loading": "Chargement...", "noData": "<PERSON><PERSON><PERSON> donnée disponible", "error": "Une erreur s'est produite", "tryAgain": "<PERSON><PERSON><PERSON><PERSON>", "refresh": "Actualiser", "close": "<PERSON><PERSON><PERSON>", "save": "Enregistrer", "cancel": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "view": "Voir", "search": "Rechercher...", "notifications": "Notifications", "userMenu": "<PERSON><PERSON> utilisateur", "profile": "Mon profil", "logout": "Se déconnecter", "toggleMobileMenu": "Basculer le menu mobile", "dashboardNavigation": "Navigation du tableau de bord", "version": "Version 1.0.0", "adminDashboard": "Tableau de bord Admin"}}}