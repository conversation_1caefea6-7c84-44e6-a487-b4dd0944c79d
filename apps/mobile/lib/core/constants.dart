const String baseUrlWeather = 'https://api.open-meteo.com';
const String baseUrlGeocoding = 'https://geocoding-api.open-meteo.com';

const timeoutDuration = Duration(seconds: 20);
const timeoutMessage = "Veuillez vérifier votre connexion internet";
const noInternetMessage = 'Veuillez vérifier votre connexion internet';
const requestMessage = "The requested information could not be found";
const accessDenied = "Access denied";
const conflictOccured = "Conflict occured";
const internalServerError =
    "Une erreur interne s'est produite. Reessayer plus tard";
