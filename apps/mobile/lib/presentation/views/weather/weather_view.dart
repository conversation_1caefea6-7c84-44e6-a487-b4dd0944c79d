import 'package:flutter/material.dart';
import 'package:mobile/domain/services/weather_service.dart';
import 'package:mobile/presentation/views/weather/widgets/weather_empty.dart';
import 'package:mobile/presentation/views/weather/widgets/weather_error.dart';
import 'package:mobile/presentation/views/weather/widgets/weather_loading.dart';
import 'package:mobile/presentation/views/weather/widgets/weather_populated.dart';
import 'package:stacked/stacked.dart';

import 'weather_viewmodel.dart';

class WeatherView extends StackedView<WeatherViewModel> {
  const WeatherView({super.key});

  @override
  Widget builder(
    BuildContext context,
    WeatherViewModel viewModel,
    Widget? child,
  ) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => viewModel.showSettingsView(),
          ),
        ],
      ),
      body: Center(
        child: Builder(
          builder: (context) {
            return switch (viewModel.status) {
              WeatherStatus.initial => const WeatherEmpty(),
              WeatherStatus.loading => const WeatherLoading(),
              WeatherStatus.failure => const WeatherError(),
              WeatherStatus.success => WeatherPopulated(
                  weather: viewModel.currentWeather,
                  units: viewModel.temperatureUnits,
                  onRefresh: () async {
                    viewModel.refreshWeather();
                  },
                ),
            };
          },
        ),
      ),
      floatingActionButton: FloatingActionButton(
        child: const Icon(Icons.search, semanticLabel: 'Search'),
        onPressed: () async {
          viewModel.showSearchPage();
        },
      ),
    );
  }

  @override
  WeatherViewModel viewModelBuilder(
    BuildContext context,
  ) =>
      WeatherViewModel();
}
