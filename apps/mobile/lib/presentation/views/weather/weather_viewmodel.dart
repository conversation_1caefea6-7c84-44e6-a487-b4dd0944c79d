import 'package:mobile/app/app.locator.dart';
import 'package:mobile/app/app.router.dart';
import 'package:mobile/domain/models/temperature.dart';
import 'package:mobile/domain/models/weather.dart';
import 'package:mobile/domain/services/weather_service.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';

class WeatherViewModel extends ReactiveViewModel {
  TemperatureUnits get temperatureUnits => _weatherService.temperatureUnits;
  Weather get currentWeather => _weatherService.currentWeather;

  WeatherStatus get status => _weatherService.status;
  final _weatherService = locator<WeatherService>();
  final _navigationService = locator<NavigationService>();

  final _snackbarService = locator<SnackbarService>();

  Future<void> fetchWeather(String? city) async {
    if (city == null || city.isEmpty) return;

    try {
      setBusy(true);

      await _weatherService.fetchWeather(city);
    } on Exception catch (e) {
      _snackbarService.showSnackbar(
        message: 'Error fetching weather: ${e.toString()}',
      );
    } finally {
      setBusy(false);
    }
  }

  Future<void> refreshWeather() async {
    if (!status.isSuccess) return;
    if (currentWeather == Weather.empty) return;
    try {
      await _weatherService.refreshWeather();
    } on Exception {
      //
    }
  }

  void showSettingsView() {
    _navigationService.navigateToSettingsView();
  }

  Future<void> showSearchPage() async {
    final result = await _navigationService.navigateToSearchView();
    if (result != null && result is String && result.isNotEmpty) {
      await fetchWeather(result);
    }
  }

  void toggleUnits() {
    _weatherService.toggleUnits();
  }

  @override
  List<ListenableServiceMixin> get listenableServices => [_weatherService];
}
