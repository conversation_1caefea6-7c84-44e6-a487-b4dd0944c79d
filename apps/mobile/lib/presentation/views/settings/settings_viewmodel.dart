import 'package:mobile/app/app.locator.dart';
import 'package:mobile/domain/models/temperature.dart';
import 'package:mobile/domain/services/weather_service.dart';
import 'package:stacked/stacked.dart';

class SettingsViewModel extends ReactiveViewModel {
  final _weatherService = locator<WeatherService>();
  TemperatureUnits get temperatureUnits => _weatherService.temperatureUnits;

  void toggleUnits() {
    _weatherService.toggleUnits();
  }

  @override
  List<ListenableServiceMixin> get listenableServices => [_weatherService];
}
