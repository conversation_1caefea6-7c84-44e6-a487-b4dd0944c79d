
import 'package:flutter/material.dart';
import 'package:mobile/app/app.locator.dart';
import 'package:mobile/domain/models/weather.dart';
import 'package:mobile/domain/models/weather_condition.dart';
import 'package:mobile/domain/services/weather_service.dart';
import 'package:stacked/stacked.dart';

class MainViewmodel extends ReactiveViewModel {
  final _weatherService = locator<WeatherService>();
  Weather get currentWeather => _weatherService.currentWeather;

  @override
  List<ListenableServiceMixin> get listenableServices => [_weatherService];
}


extension WeatherColor on Weather {
  Color get toColor {
    switch (condition) {
      case WeatherCondition.clear:
        return Colors.yellow;
      case WeatherCondition.snowy:
        return Colors.lightBlueAccent;
      case WeatherCondition.cloudy:
        return Colors.blueGrey;
      case WeatherCondition.rainy:
        return Colors.indigoAccent;
      case WeatherCondition.unknown:
        return Colors.cyan;
    }
  }
}
