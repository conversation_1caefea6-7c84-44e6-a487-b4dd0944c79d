import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile/app/app.bottomsheets.dart';
import 'package:mobile/app/app.dialogs.dart';
import 'package:mobile/app/app.locator.dart';
import 'package:mobile/app/app.router.dart';
import 'package:mobile/core/setup/setup_snackbar.dart';
import 'package:mobile/presentation/main_viewmodel.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await setupLocator();
  setupDialogUi();
  setupBottomSheetUi();
  setupSnackbarUi();
  runApp(const MainApp());
}

class MainApp extends StatelessWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<MainViewmodel>.reactive(
        viewModelBuilder: () => MainViewmodel(),
        builder: (context, viewModel, child) {
          final seedColor = viewModel.currentWeather.toColor;
          return MaterialApp(
            theme: ThemeData(
              appBarTheme: const AppBarTheme(
                backgroundColor: Colors.transparent,
                elevation: 0,
              ),
              colorScheme: ColorScheme.fromSeed(seedColor: seedColor),
              textTheme: GoogleFonts.rajdhaniTextTheme(),
            ),
            initialRoute: Routes.weatherView,
            onGenerateRoute: StackedRouter().onGenerateRoute,
            navigatorKey: StackedService.navigatorKey,
            navigatorObservers: [StackedService.routeObserver],
          );
        });
  }
}
