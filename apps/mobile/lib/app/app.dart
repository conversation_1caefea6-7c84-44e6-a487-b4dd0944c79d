import 'package:mobile/presentation/bottom_sheets/notice/notice_sheet.dart';
import 'package:mobile/presentation/dialogs/info_alert/info_alert_dialog.dart';
import 'package:mobile/presentation/views/home/<USER>';
import 'package:mobile/presentation/views/startup/startup_view.dart';
import 'package:stacked/stacked_annotations.dart';
import 'package:stacked_services/stacked_services.dart';
import 'package:mobile/presentation/views/weather/weather_view.dart';
import 'package:mobile/domain/services/weather_service.dart';
import 'package:mobile/presentation/views/settings/settings_view.dart';
import 'package:mobile/presentation/views/search/search_view.dart';
// @stacked-import

@StackedApp(
  routes: [
    MaterialRoute(page: HomeView),
    MaterialRoute(page: StartupView),
    MaterialRoute(page: WeatherView),
    MaterialRoute(page: SettingsView),
    MaterialRoute(page: SearchView),
// @stacked-route
  ],
  dependencies: [
    <PERSON><PERSON><PERSON><PERSON><PERSON>(classType: BottomSheetService),
    <PERSON><PERSON><PERSON><PERSON><PERSON>(classType: DialogService),
    <PERSON><PERSON><PERSON><PERSON><PERSON>(classType: NavigationService),
    <PERSON><PERSON><PERSON><PERSON><PERSON>(classType: WeatherService),
    <PERSON><PERSON><PERSON><PERSON><PERSON>(classType: SnackbarService),
// @stacked-service
  ],
  bottomsheets: [
    StackedBottomsheet(classType: NoticeSheet),
    // @stacked-bottom-sheet
  ],
  dialogs: [
    StackedDialog(classType: InfoAlertDialog),
    // @stacked-dialog
  ],
)
class App {}
