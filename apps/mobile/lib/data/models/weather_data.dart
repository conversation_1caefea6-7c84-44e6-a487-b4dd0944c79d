// To parse this JSON data, do
//
//     final weather = weatherFromJson(jsonString);

import 'dart:convert';

WeatherData weatherDataFromJson(String str) =>
    WeatherData.fromJson(json.decode(str));

String weatherDataToJson(WeatherData data) => json.encode(data.toJson());

class WeatherData {
  double temperature;
  int weathercode;

  WeatherData({
    required this.temperature,
    required this.weathercode,
  });

  factory WeatherData.fromJson(Map<String, dynamic> json) => WeatherData(
        temperature: json["temperature"]?.toDouble(),
        weathercode: json["weathercode"],
      );

  Map<String, dynamic> toJson() => {
        "temperature": temperature,
        "weathercode": weathercode,
      };
}
