import 'package:mobile/data/data_source/remote/meteo_data_source.dart';
import 'package:mobile/data/models/location_data.dart';
import 'package:mobile/data/models/weather_data.dart';
import 'package:mobile/domain/repository/meteo_repo.dart';

class MeteoRepositoryImpl extends MeteoRepository {
  final MeteoDataSource _meteoDataSource;

  MeteoRepositoryImpl({MeteoDataSource? meteoDataSource})
      : _meteoDataSource = meteoDataSource ?? MeteoDataSource();
  @override
  Future<WeatherData> getWeather(Map<String, dynamic> params) async {
    final response = await _meteoDataSource.getWeather(params);
    final result = response.data['current_weather'] as Map<String, dynamic>;
    return WeatherData.fromJson(result);
  }

  @override
  Future<LocationData> locationSearch(Map<String, dynamic> params) async {
    final response = await _meteoDataSource.locationSearch(params);
    final result = (response.data['results'] as List<dynamic>?) ;
    if (result!= null && result.isNotEmpty) {
      return LocationData.fromJson(result[0] as Map<String, dynamic>);
    } else {
      throw Exception('No location found');
    }
  }
}
