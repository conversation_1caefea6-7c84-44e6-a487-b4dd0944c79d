import 'package:dio/dio.dart';

import '../../../core/constants.dart';

class Api {
  // final dio = createDio();
  // final tokenDio = createTokenDio();

  Api._internal();

  static final _singleton = Api._internal();

  factory Api() => _singleton;

  //Dio for unauthenticated user
  static Dio createTokenDio() {
    var dio = Dio(BaseOptions(
        baseUrl: '',
        receiveTimeout: const Duration(seconds: 30),
        connectTimeout: const Duration(seconds: 30),
        sendTimeout: const Duration(seconds: 30),
        headers: {
          'Accept': "application/json",
          'Content-Type': 'application/json',
          'Charset': 'utf-8',
        }));

    dio.interceptors.addAll({BaseInterceptor(dio)});

    return dio;
  }

  //Dio for authenticated user
  static Dio createDio() {
    var dio = Dio(BaseOptions(
        baseUrl: '',
        receiveTimeout: const Duration(milliseconds: 30000),
        connectTimeout: const Duration(milliseconds: 30000),
        sendTimeout: const Duration(milliseconds: 30000),
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Charset': 'utf-8',
        }));

    dio.interceptors.addAll({AppInterceptors(dio)});

    return dio;
  }
}

///Interceptors
class BaseInterceptor extends Interceptor {
  final Dio dio;

  BaseInterceptor(this.dio);

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    final responseData = err.response?.data;

    switch (err.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        throw DeadlineExceededException(err.requestOptions);
      case DioExceptionType.badResponse:
        switch (err.response?.statusCode) {
          case 400:
            // Sentry.captureException(err);

            throw BadRequestException(err.requestOptions, 'Invalid request');

          case 403:
            throw UnauthorizedException(
                err.requestOptions, responseData["message"]);

          case 401:
            throw UnauthorizedException(err.requestOptions, 'Access denied');
          case 404:
            //Sentry.captureException(err);
            throw NotFoundException(err.requestOptions);

          case 406:
            throw NotAcceptException(
                err.requestOptions, responseData['message']);
          case 409:
            throw ConflictException(
                err.requestOptions, responseData['message']);

          case 422:
            // final results = responseData['errors'] as List<dynamic>;

            throw UnprocessableEntityException(err.requestOptions, []);
          case 500:
            //  Sentry.captureException(err);
            throw InternalServerErrorException(err.requestOptions);
        }
        break;

      case DioExceptionType.cancel:
        break;
      case DioExceptionType.unknown:
        throw NoInternetConnectionException(err.requestOptions);
      default:
        throw NoInternetConnectionException(err.requestOptions);
    }
  }
}

class AppInterceptors extends BaseInterceptor {
  AppInterceptors(super.dio);
}

class NotAcceptException extends DioException {
  final String msg;

  NotAcceptException(RequestOptions r, this.msg) : super(requestOptions: r);

  @override
  String toString() {
    return msg.isEmpty ? 'Not acceptable' : msg;
  }
}

class BadRequestException extends DioException {
  final String msg;

  BadRequestException(RequestOptions r, this.msg) : super(requestOptions: r);

  @override
  String toString() {
    return msg.isEmpty ? 'Invalid request' : msg;
  }
}

class UnprocessableEntityException extends DioException {
  final List<dynamic> errors;

  UnprocessableEntityException(RequestOptions r, this.errors)
      : super(requestOptions: r);
}

class InternalServerErrorException extends DioException {
  InternalServerErrorException(RequestOptions r) : super(requestOptions: r);

  @override
  String toString() {
    return internalServerError;
  }
}

class ConflictException extends DioException {
  final String msg;

  ConflictException(RequestOptions r, this.msg) : super(requestOptions: r);

  @override
  String toString() {
    return msg.isEmpty ? conflictOccured : msg;
  }
}

class UnauthorizedException extends DioException {
  final String msg;

  UnauthorizedException(RequestOptions r, this.msg) : super(requestOptions: r);

  @override
  String toString() {
    return msg.isEmpty ? accessDenied : msg;
  }
}

class NotFoundException extends DioException {
  NotFoundException(RequestOptions r) : super(requestOptions: r);

  @override
  String toString() {
    return requestMessage;
  }
}

class NoInternetConnectionException extends DioException {
  NoInternetConnectionException(RequestOptions r) : super(requestOptions: r);

  @override
  String toString() {
    return noInternetMessage;
  }
}

class DeadlineExceededException extends DioException {
  DeadlineExceededException(RequestOptions r) : super(requestOptions: r);

  @override
  String toString() {
    return timeoutMessage;
  }
}
