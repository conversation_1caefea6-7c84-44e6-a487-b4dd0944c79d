import 'package:dio/dio.dart';
import 'package:mobile/core/constants.dart';

class MeteoDataSource {
  MeteoDataSource();

  //search location
  Future<Response> locationSearch(Map<String, dynamic> data) async {
    try {
      final dio = Dio();
      final response =
          await dio.get("$baseUrlGeocoding/v1/search", queryParameters: data);
      return response;
    } catch (error) {
      rethrow;
    }
  }

  //Fetch weather data
  Future<Response> getWeather(Map<String, dynamic> data) async {
    try {
      final dio = Dio();
      final response =
          await dio.get("$baseUrlWeather/v1/forecast", queryParameters: data);
      return response;
    } catch (error) {
      rethrow;
    }
  }
}
