//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_import

import 'package:one_of_serializer/any_of_serializer.dart';
import 'package:one_of_serializer/one_of_serializer.dart';
import 'package:built_collection/built_collection.dart';
import 'package:built_value/json_object.dart';
import 'package:built_value/serializer.dart';
import 'package:built_value/standard_json_plugin.dart';
import 'package:built_value/iso_8601_date_time_serializer.dart';
import 'package:proamlink_api/lib/date_serializer.dart';
import 'package:proamlink_api/lib/model/date.dart';

import 'package:proamlink_api/lib/model/error_badrequest.dart';
import 'package:proamlink_api/lib/model/error_forbidden.dart';
import 'package:proamlink_api/lib/model/error_internalservererror.dart';
import 'package:proamlink_api/lib/model/error_internalservererror_issues_inner.dart';
import 'package:proamlink_api/lib/model/error_notfound.dart';
import 'package:proamlink_api/lib/model/error_unauthorized.dart';
import 'package:proamlink_api/lib/model/system_health200_response.dart';
import 'package:proamlink_api/lib/model/system_health200_response_services.dart';
import 'package:proamlink_api/lib/model/test_complex_params_request.dart';
import 'package:proamlink_api/lib/model/test_complex_params_request_object_param.dart';
import 'package:proamlink_api/lib/model/test_error_test_request.dart';
import 'package:proamlink_api/lib/model/test_public_get200_response.dart';
import 'package:proamlink_api/lib/model/test_public_post_request.dart';

part 'serializers.g.dart';

@SerializersFor([
  ErrorBADREQUEST,
  ErrorFORBIDDEN,
  ErrorINTERNALSERVERERROR,
  ErrorINTERNALSERVERERRORIssuesInner,
  ErrorNOTFOUND,
  ErrorUNAUTHORIZED,
  SystemHealth200Response,
  SystemHealth200ResponseServices,
  TestComplexParamsRequest,
  TestComplexParamsRequestObjectParam,
  TestErrorTestRequest,
  TestPublicGet200Response,
  TestPublicPostRequest,
])
Serializers serializers = (_$serializers.toBuilder()
      ..add(const OneOfSerializer())
      ..add(const AnyOfSerializer())
      ..add(const DateSerializer())
      ..add(Iso8601DateTimeSerializer())
    ).build();

Serializers standardSerializers =
    (serializers.toBuilder()..addPlugin(StandardJsonPlugin())).build();
