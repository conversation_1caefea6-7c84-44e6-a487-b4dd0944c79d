//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'test_error_test_request.g.dart';

/// TestErrorTestRequest
///
/// Properties:
/// * [errorType] - Type of error to simulate
@BuiltValue()
abstract class TestErrorTestRequest implements Built<TestErrorTestRequest, TestErrorTestRequestBuilder> {
  /// Type of error to simulate
  @BuiltValueField(wireName: r'errorType')
  TestErrorTestRequestErrorTypeEnum get errorType;
  // enum errorTypeEnum {  validation,  server,  not_found,  };

  TestErrorTestRequest._();

  factory TestErrorTestRequest([void updates(TestErrorTestRequestBuilder b)]) = _$TestErrorTestRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(TestErrorTestRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<TestErrorTestRequest> get serializer => _$TestErrorTestRequestSerializer();
}

class _$TestErrorTestRequestSerializer implements PrimitiveSerializer<TestErrorTestRequest> {
  @override
  final Iterable<Type> types = const [TestErrorTestRequest, _$TestErrorTestRequest];

  @override
  final String wireName = r'TestErrorTestRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    TestErrorTestRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'errorType';
    yield serializers.serialize(
      object.errorType,
      specifiedType: const FullType(TestErrorTestRequestErrorTypeEnum),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    TestErrorTestRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required TestErrorTestRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'errorType':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(TestErrorTestRequestErrorTypeEnum),
          ) as TestErrorTestRequestErrorTypeEnum;
          result.errorType = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  TestErrorTestRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = TestErrorTestRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class TestErrorTestRequestErrorTypeEnum extends EnumClass {

  /// Type of error to simulate
  @BuiltValueEnumConst(wireName: r'validation')
  static const TestErrorTestRequestErrorTypeEnum validation = _$testErrorTestRequestErrorTypeEnum_validation;
  /// Type of error to simulate
  @BuiltValueEnumConst(wireName: r'server')
  static const TestErrorTestRequestErrorTypeEnum server = _$testErrorTestRequestErrorTypeEnum_server;
  /// Type of error to simulate
  @BuiltValueEnumConst(wireName: r'not_found')
  static const TestErrorTestRequestErrorTypeEnum notFound = _$testErrorTestRequestErrorTypeEnum_notFound;

  static Serializer<TestErrorTestRequestErrorTypeEnum> get serializer => _$testErrorTestRequestErrorTypeEnumSerializer;

  const TestErrorTestRequestErrorTypeEnum._(String name): super(name);

  static BuiltSet<TestErrorTestRequestErrorTypeEnum> get values => _$testErrorTestRequestErrorTypeEnumValues;
  static TestErrorTestRequestErrorTypeEnum valueOf(String name) => _$testErrorTestRequestErrorTypeEnumValueOf(name);
}

