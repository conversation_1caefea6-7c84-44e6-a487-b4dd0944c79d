//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'error_internalservererror_issues_inner.g.dart';

/// ErrorINTERNALSERVERERRORIssuesInner
///
/// Properties:
/// * [message] 
@BuiltValue()
abstract class ErrorINTERNALSERVERERRORIssuesInner implements Built<ErrorINTERNALSERVERERRORIssuesInner, ErrorINTERNALSERVERERRORIssuesInnerBuilder> {
  @BuiltValueField(wireName: r'message')
  String get message;

  ErrorINTERNALSERVERERRORIssuesInner._();

  factory ErrorINTERNALSERVERERRORIssuesInner([void updates(ErrorINTERNALSERVERERRORIssuesInnerBuilder b)]) = _$ErrorINTERNALSERVERERRORIssuesInner;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(ErrorINTERNALSERVERERRORIssuesInnerBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<ErrorINTERNALSERVERERRORIssuesInner> get serializer => _$ErrorINTERNALSERVERERRORIssuesInnerSerializer();
}

class _$ErrorINTERNALSERVERERRORIssuesInnerSerializer implements PrimitiveSerializer<ErrorINTERNALSERVERERRORIssuesInner> {
  @override
  final Iterable<Type> types = const [ErrorINTERNALSERVERERRORIssuesInner, _$ErrorINTERNALSERVERERRORIssuesInner];

  @override
  final String wireName = r'ErrorINTERNALSERVERERRORIssuesInner';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    ErrorINTERNALSERVERERRORIssuesInner object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'message';
    yield serializers.serialize(
      object.message,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    ErrorINTERNALSERVERERRORIssuesInner object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required ErrorINTERNALSERVERERRORIssuesInnerBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.message = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  ErrorINTERNALSERVERERRORIssuesInner deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = ErrorINTERNALSERVERERRORIssuesInnerBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

