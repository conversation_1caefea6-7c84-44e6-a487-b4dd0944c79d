//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'system_health200_response_services.g.dart';

/// État des services externes
///
/// Properties:
/// * [database] - État de la base de données
/// * [redis] - État du cache Redis
@BuiltValue()
abstract class SystemHealth200ResponseServices implements Built<SystemHealth200ResponseServices, SystemHealth200ResponseServicesBuilder> {
  /// État de la base de données
  @BuiltValueField(wireName: r'database')
  SystemHealth200ResponseServicesDatabaseEnum get database;
  // enum databaseEnum {  connected,  };

  /// État du cache Redis
  @BuiltValueField(wireName: r'redis')
  SystemHealth200ResponseServicesRedisEnum get redis;
  // enum redisEnum {  connected,  };

  SystemHealth200ResponseServices._();

  factory SystemHealth200ResponseServices([void updates(SystemHealth200ResponseServicesBuilder b)]) = _$SystemHealth200ResponseServices;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(SystemHealth200ResponseServicesBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<SystemHealth200ResponseServices> get serializer => _$SystemHealth200ResponseServicesSerializer();
}

class _$SystemHealth200ResponseServicesSerializer implements PrimitiveSerializer<SystemHealth200ResponseServices> {
  @override
  final Iterable<Type> types = const [SystemHealth200ResponseServices, _$SystemHealth200ResponseServices];

  @override
  final String wireName = r'SystemHealth200ResponseServices';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    SystemHealth200ResponseServices object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'database';
    yield serializers.serialize(
      object.database,
      specifiedType: const FullType(SystemHealth200ResponseServicesDatabaseEnum),
    );
    yield r'redis';
    yield serializers.serialize(
      object.redis,
      specifiedType: const FullType(SystemHealth200ResponseServicesRedisEnum),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    SystemHealth200ResponseServices object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required SystemHealth200ResponseServicesBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'database':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(SystemHealth200ResponseServicesDatabaseEnum),
          ) as SystemHealth200ResponseServicesDatabaseEnum;
          result.database = valueDes;
          break;
        case r'redis':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(SystemHealth200ResponseServicesRedisEnum),
          ) as SystemHealth200ResponseServicesRedisEnum;
          result.redis = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  SystemHealth200ResponseServices deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = SystemHealth200ResponseServicesBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class SystemHealth200ResponseServicesDatabaseEnum extends EnumClass {

  /// État de la base de données
  @BuiltValueEnumConst(wireName: r'connected')
  static const SystemHealth200ResponseServicesDatabaseEnum connected = _$systemHealth200ResponseServicesDatabaseEnum_connected;

  static Serializer<SystemHealth200ResponseServicesDatabaseEnum> get serializer => _$systemHealth200ResponseServicesDatabaseEnumSerializer;

  const SystemHealth200ResponseServicesDatabaseEnum._(String name): super(name);

  static BuiltSet<SystemHealth200ResponseServicesDatabaseEnum> get values => _$systemHealth200ResponseServicesDatabaseEnumValues;
  static SystemHealth200ResponseServicesDatabaseEnum valueOf(String name) => _$systemHealth200ResponseServicesDatabaseEnumValueOf(name);
}

class SystemHealth200ResponseServicesRedisEnum extends EnumClass {

  /// État du cache Redis
  @BuiltValueEnumConst(wireName: r'connected')
  static const SystemHealth200ResponseServicesRedisEnum connected = _$systemHealth200ResponseServicesRedisEnum_connected;

  static Serializer<SystemHealth200ResponseServicesRedisEnum> get serializer => _$systemHealth200ResponseServicesRedisEnumSerializer;

  const SystemHealth200ResponseServicesRedisEnum._(String name): super(name);

  static BuiltSet<SystemHealth200ResponseServicesRedisEnum> get values => _$systemHealth200ResponseServicesRedisEnumValues;
  static SystemHealth200ResponseServicesRedisEnum valueOf(String name) => _$systemHealth200ResponseServicesRedisEnumValueOf(name);
}

