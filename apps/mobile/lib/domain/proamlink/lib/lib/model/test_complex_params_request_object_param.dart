//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'test_complex_params_request_object_param.g.dart';

/// TestComplexParamsRequestObjectParam
///
/// Properties:
/// * [nested] 
/// * [value] 
@BuiltValue()
abstract class TestComplexParamsRequestObjectParam implements Built<TestComplexParamsRequestObjectParam, TestComplexParamsRequestObjectParamBuilder> {
  @BuiltValueField(wireName: r'nested')
  String get nested;

  @BuiltValueField(wireName: r'value')
  num get value;

  TestComplexParamsRequestObjectParam._();

  factory TestComplexParamsRequestObjectParam([void updates(TestComplexParamsRequestObjectParamBuilder b)]) = _$TestComplexParamsRequestObjectParam;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(TestComplexParamsRequestObjectParamBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<TestComplexParamsRequestObjectParam> get serializer => _$TestComplexParamsRequestObjectParamSerializer();
}

class _$TestComplexParamsRequestObjectParamSerializer implements PrimitiveSerializer<TestComplexParamsRequestObjectParam> {
  @override
  final Iterable<Type> types = const [TestComplexParamsRequestObjectParam, _$TestComplexParamsRequestObjectParam];

  @override
  final String wireName = r'TestComplexParamsRequestObjectParam';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    TestComplexParamsRequestObjectParam object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'nested';
    yield serializers.serialize(
      object.nested,
      specifiedType: const FullType(String),
    );
    yield r'value';
    yield serializers.serialize(
      object.value,
      specifiedType: const FullType(num),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    TestComplexParamsRequestObjectParam object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required TestComplexParamsRequestObjectParamBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'nested':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.nested = valueDes;
          break;
        case r'value':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(num),
          ) as num;
          result.value = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  TestComplexParamsRequestObjectParam deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = TestComplexParamsRequestObjectParamBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

