//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:proamlink_api/lib/model/system_health200_response_services.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'system_health200_response.g.dart';

/// SystemHealth200Response
///
/// Properties:
/// * [status] - État de l'API
/// * [timestamp] - Horodatage de la vérification
/// * [version] - Version de l'API
/// * [environment] - Environnement d'exécution
/// * [services] 
@BuiltValue()
abstract class SystemHealth200Response implements Built<SystemHealth200Response, SystemHealth200ResponseBuilder> {
  /// État de l'API
  @BuiltValueField(wireName: r'status')
  SystemHealth200ResponseStatusEnum get status;
  // enum statusEnum {  ok,  };

  /// Horodatage de la vérification
  @BuiltValueField(wireName: r'timestamp')
  String get timestamp;

  /// Version de l'API
  @BuiltValueField(wireName: r'version')
  String get version;

  /// Environnement d'exécution
  @BuiltValueField(wireName: r'environment')
  String get environment;

  @BuiltValueField(wireName: r'services')
  SystemHealth200ResponseServices get services;

  SystemHealth200Response._();

  factory SystemHealth200Response([void updates(SystemHealth200ResponseBuilder b)]) = _$SystemHealth200Response;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(SystemHealth200ResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<SystemHealth200Response> get serializer => _$SystemHealth200ResponseSerializer();
}

class _$SystemHealth200ResponseSerializer implements PrimitiveSerializer<SystemHealth200Response> {
  @override
  final Iterable<Type> types = const [SystemHealth200Response, _$SystemHealth200Response];

  @override
  final String wireName = r'SystemHealth200Response';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    SystemHealth200Response object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'status';
    yield serializers.serialize(
      object.status,
      specifiedType: const FullType(SystemHealth200ResponseStatusEnum),
    );
    yield r'timestamp';
    yield serializers.serialize(
      object.timestamp,
      specifiedType: const FullType(String),
    );
    yield r'version';
    yield serializers.serialize(
      object.version,
      specifiedType: const FullType(String),
    );
    yield r'environment';
    yield serializers.serialize(
      object.environment,
      specifiedType: const FullType(String),
    );
    yield r'services';
    yield serializers.serialize(
      object.services,
      specifiedType: const FullType(SystemHealth200ResponseServices),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    SystemHealth200Response object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required SystemHealth200ResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'status':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(SystemHealth200ResponseStatusEnum),
          ) as SystemHealth200ResponseStatusEnum;
          result.status = valueDes;
          break;
        case r'timestamp':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.timestamp = valueDes;
          break;
        case r'version':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.version = valueDes;
          break;
        case r'environment':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.environment = valueDes;
          break;
        case r'services':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(SystemHealth200ResponseServices),
          ) as SystemHealth200ResponseServices;
          result.services.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  SystemHealth200Response deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = SystemHealth200ResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class SystemHealth200ResponseStatusEnum extends EnumClass {

  /// État de l'API
  @BuiltValueEnumConst(wireName: r'ok')
  static const SystemHealth200ResponseStatusEnum ok = _$systemHealth200ResponseStatusEnum_ok;

  static Serializer<SystemHealth200ResponseStatusEnum> get serializer => _$systemHealth200ResponseStatusEnumSerializer;

  const SystemHealth200ResponseStatusEnum._(String name): super(name);

  static BuiltSet<SystemHealth200ResponseStatusEnum> get values => _$systemHealth200ResponseStatusEnumValues;
  static SystemHealth200ResponseStatusEnum valueOf(String name) => _$systemHealth200ResponseStatusEnumValueOf(name);
}

