//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:proamlink_api/lib/model/test_complex_params_request_object_param.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'test_complex_params_request.g.dart';

/// TestComplexParamsRequest
///
/// Properties:
/// * [stringParam] 
/// * [numberParam] 
/// * [booleanParam] 
/// * [dateParam] 
/// * [enumParam] 
/// * [arrayParam] 
/// * [objectParam] 
/// * [optionalParam] 
@BuiltValue()
abstract class TestComplexParamsRequest implements Built<TestComplexParamsRequest, TestComplexParamsRequestBuilder> {
  @BuiltValueField(wireName: r'stringParam')
  String get stringParam;

  @BuiltValueField(wireName: r'numberParam')
  num get numberParam;

  @BuiltValueField(wireName: r'booleanParam')
  bool get booleanParam;

  @BuiltValueField(wireName: r'dateParam')
  DateTime get dateParam;

  @BuiltValueField(wireName: r'enumParam')
  TestComplexParamsRequestEnumParamEnum get enumParam;
  // enum enumParamEnum {  option1,  option2,  option3,  };

  @BuiltValueField(wireName: r'arrayParam')
  BuiltList<String> get arrayParam;

  @BuiltValueField(wireName: r'objectParam')
  TestComplexParamsRequestObjectParam get objectParam;

  @BuiltValueField(wireName: r'optionalParam')
  String? get optionalParam;

  TestComplexParamsRequest._();

  factory TestComplexParamsRequest([void updates(TestComplexParamsRequestBuilder b)]) = _$TestComplexParamsRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(TestComplexParamsRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<TestComplexParamsRequest> get serializer => _$TestComplexParamsRequestSerializer();
}

class _$TestComplexParamsRequestSerializer implements PrimitiveSerializer<TestComplexParamsRequest> {
  @override
  final Iterable<Type> types = const [TestComplexParamsRequest, _$TestComplexParamsRequest];

  @override
  final String wireName = r'TestComplexParamsRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    TestComplexParamsRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'stringParam';
    yield serializers.serialize(
      object.stringParam,
      specifiedType: const FullType(String),
    );
    yield r'numberParam';
    yield serializers.serialize(
      object.numberParam,
      specifiedType: const FullType(num),
    );
    yield r'booleanParam';
    yield serializers.serialize(
      object.booleanParam,
      specifiedType: const FullType(bool),
    );
    yield r'dateParam';
    yield serializers.serialize(
      object.dateParam,
      specifiedType: const FullType(DateTime),
    );
    yield r'enumParam';
    yield serializers.serialize(
      object.enumParam,
      specifiedType: const FullType(TestComplexParamsRequestEnumParamEnum),
    );
    yield r'arrayParam';
    yield serializers.serialize(
      object.arrayParam,
      specifiedType: const FullType(BuiltList, [FullType(String)]),
    );
    yield r'objectParam';
    yield serializers.serialize(
      object.objectParam,
      specifiedType: const FullType(TestComplexParamsRequestObjectParam),
    );
    if (object.optionalParam != null) {
      yield r'optionalParam';
      yield serializers.serialize(
        object.optionalParam,
        specifiedType: const FullType(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    TestComplexParamsRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required TestComplexParamsRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'stringParam':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.stringParam = valueDes;
          break;
        case r'numberParam':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(num),
          ) as num;
          result.numberParam = valueDes;
          break;
        case r'booleanParam':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(bool),
          ) as bool;
          result.booleanParam = valueDes;
          break;
        case r'dateParam':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(DateTime),
          ) as DateTime;
          result.dateParam = valueDes;
          break;
        case r'enumParam':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(TestComplexParamsRequestEnumParamEnum),
          ) as TestComplexParamsRequestEnumParamEnum;
          result.enumParam = valueDes;
          break;
        case r'arrayParam':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(String)]),
          ) as BuiltList<String>;
          result.arrayParam.replace(valueDes);
          break;
        case r'objectParam':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(TestComplexParamsRequestObjectParam),
          ) as TestComplexParamsRequestObjectParam;
          result.objectParam.replace(valueDes);
          break;
        case r'optionalParam':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.optionalParam = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  TestComplexParamsRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = TestComplexParamsRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

class TestComplexParamsRequestEnumParamEnum extends EnumClass {

  @BuiltValueEnumConst(wireName: r'option1')
  static const TestComplexParamsRequestEnumParamEnum option1 = _$testComplexParamsRequestEnumParamEnum_option1;
  @BuiltValueEnumConst(wireName: r'option2')
  static const TestComplexParamsRequestEnumParamEnum option2 = _$testComplexParamsRequestEnumParamEnum_option2;
  @BuiltValueEnumConst(wireName: r'option3')
  static const TestComplexParamsRequestEnumParamEnum option3 = _$testComplexParamsRequestEnumParamEnum_option3;

  static Serializer<TestComplexParamsRequestEnumParamEnum> get serializer => _$testComplexParamsRequestEnumParamEnumSerializer;

  const TestComplexParamsRequestEnumParamEnum._(String name): super(name);

  static BuiltSet<TestComplexParamsRequestEnumParamEnum> get values => _$testComplexParamsRequestEnumParamEnumValues;
  static TestComplexParamsRequestEnumParamEnum valueOf(String name) => _$testComplexParamsRequestEnumParamEnumValueOf(name);
}

