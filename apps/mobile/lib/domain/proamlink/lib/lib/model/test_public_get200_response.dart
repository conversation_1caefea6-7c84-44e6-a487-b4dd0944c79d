//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/json_object.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'test_public_get200_response.g.dart';

/// TestPublicGet200Response
///
/// Properties:
/// * [success] - Operation success status
/// * [data] - Response data
/// * [timestamp] - Response timestamp
@BuiltValue()
abstract class TestPublicGet200Response implements Built<TestPublicGet200Response, TestPublicGet200ResponseBuilder> {
  /// Operation success status
  @BuiltValueField(wireName: r'success')
  bool get success;

  /// Response data
  @BuiltValueField(wireName: r'data')
  JsonObject? get data;

  /// Response timestamp
  @BuiltValueField(wireName: r'timestamp')
  String get timestamp;

  TestPublicGet200Response._();

  factory TestPublicGet200Response([void updates(TestPublicGet200ResponseBuilder b)]) = _$TestPublicGet200Response;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(TestPublicGet200ResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<TestPublicGet200Response> get serializer => _$TestPublicGet200ResponseSerializer();
}

class _$TestPublicGet200ResponseSerializer implements PrimitiveSerializer<TestPublicGet200Response> {
  @override
  final Iterable<Type> types = const [TestPublicGet200Response, _$TestPublicGet200Response];

  @override
  final String wireName = r'TestPublicGet200Response';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    TestPublicGet200Response object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'success';
    yield serializers.serialize(
      object.success,
      specifiedType: const FullType(bool),
    );
    if (object.data != null) {
      yield r'data';
      yield serializers.serialize(
        object.data,
        specifiedType: const FullType.nullable(JsonObject),
      );
    }
    yield r'timestamp';
    yield serializers.serialize(
      object.timestamp,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    TestPublicGet200Response object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required TestPublicGet200ResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'success':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(bool),
          ) as bool;
          result.success = valueDes;
          break;
        case r'data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(JsonObject),
          ) as JsonObject?;
          if (valueDes == null) continue;
          result.data = valueDes;
          break;
        case r'timestamp':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.timestamp = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  TestPublicGet200Response deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = TestPublicGet200ResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

