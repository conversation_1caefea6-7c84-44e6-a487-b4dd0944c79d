//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'test_public_post_request.g.dart';

/// TestPublicPostRequest
///
/// Properties:
/// * [title] - Test title
/// * [description] - Optional description
/// * [tags] - Optional tags array
/// * [metadata] - Optional metadata object
@BuiltValue()
abstract class TestPublicPostRequest implements Built<TestPublicPostRequest, TestPublicPostRequestBuilder> {
  /// Test title
  @BuiltValueField(wireName: r'title')
  String get title;

  /// Optional description
  @BuiltValueField(wireName: r'description')
  String? get description;

  /// Optional tags array
  @BuiltValueField(wireName: r'tags')
  BuiltList<String>? get tags;

  /// Optional metadata object
  @BuiltValueField(wireName: r'metadata')
  BuiltMap<String, String>? get metadata;

  TestPublicPostRequest._();

  factory TestPublicPostRequest([void updates(TestPublicPostRequestBuilder b)]) = _$TestPublicPostRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(TestPublicPostRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<TestPublicPostRequest> get serializer => _$TestPublicPostRequestSerializer();
}

class _$TestPublicPostRequestSerializer implements PrimitiveSerializer<TestPublicPostRequest> {
  @override
  final Iterable<Type> types = const [TestPublicPostRequest, _$TestPublicPostRequest];

  @override
  final String wireName = r'TestPublicPostRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    TestPublicPostRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'title';
    yield serializers.serialize(
      object.title,
      specifiedType: const FullType(String),
    );
    if (object.description != null) {
      yield r'description';
      yield serializers.serialize(
        object.description,
        specifiedType: const FullType(String),
      );
    }
    if (object.tags != null) {
      yield r'tags';
      yield serializers.serialize(
        object.tags,
        specifiedType: const FullType(BuiltList, [FullType(String)]),
      );
    }
    if (object.metadata != null) {
      yield r'metadata';
      yield serializers.serialize(
        object.metadata,
        specifiedType: const FullType(BuiltMap, [FullType(String), FullType(String)]),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    TestPublicPostRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required TestPublicPostRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'title':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.title = valueDes;
          break;
        case r'description':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.description = valueDes;
          break;
        case r'tags':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(String)]),
          ) as BuiltList<String>;
          result.tags.replace(valueDes);
          break;
        case r'metadata':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltMap, [FullType(String), FullType(String)]),
          ) as BuiltMap<String, String>;
          result.metadata.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  TestPublicPostRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = TestPublicPostRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

