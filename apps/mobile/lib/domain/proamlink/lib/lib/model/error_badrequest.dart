//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:proamlink_api/lib/model/error_internalservererror_issues_inner.dart';
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'error_badrequest.g.dart';

/// The error information
///
/// Properties:
/// * [message] - The error message
/// * [code] - The error code
/// * [issues] - An array of issues that were responsible for the error
@BuiltValue()
abstract class ErrorBADREQUEST implements Built<ErrorBADREQUEST, ErrorBADREQUESTBuilder> {
  /// The error message
  @BuiltValueField(wireName: r'message')
  String get message;

  /// The error code
  @BuiltValueField(wireName: r'code')
  String get code;

  /// An array of issues that were responsible for the error
  @BuiltValueField(wireName: r'issues')
  BuiltList<ErrorINTERNALSERVERERRORIssuesInner>? get issues;

  ErrorBADREQUEST._();

  factory ErrorBADREQUEST([void updates(ErrorBADREQUESTBuilder b)]) = _$ErrorBADREQUEST;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(ErrorBADREQUESTBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<ErrorBADREQUEST> get serializer => _$ErrorBADREQUESTSerializer();
}

class _$ErrorBADREQUESTSerializer implements PrimitiveSerializer<ErrorBADREQUEST> {
  @override
  final Iterable<Type> types = const [ErrorBADREQUEST, _$ErrorBADREQUEST];

  @override
  final String wireName = r'ErrorBADREQUEST';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    ErrorBADREQUEST object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'message';
    yield serializers.serialize(
      object.message,
      specifiedType: const FullType(String),
    );
    yield r'code';
    yield serializers.serialize(
      object.code,
      specifiedType: const FullType(String),
    );
    if (object.issues != null) {
      yield r'issues';
      yield serializers.serialize(
        object.issues,
        specifiedType: const FullType(BuiltList, [FullType(ErrorINTERNALSERVERERRORIssuesInner)]),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    ErrorBADREQUEST object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required ErrorBADREQUESTBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.message = valueDes;
          break;
        case r'code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.code = valueDes;
          break;
        case r'issues':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(ErrorINTERNALSERVERERRORIssuesInner)]),
          ) as BuiltList<ErrorINTERNALSERVERERRORIssuesInner>;
          result.issues.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  ErrorBADREQUEST deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = ErrorBADREQUESTBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

