# proamlink_api (EXPERIMENTAL)

    API REST mobile pour l'application ProAmLink - Version optimisée pour les applications mobiles.

    Cette API fournit des endpoints optimisés pour :
    - Authentification mobile et gestion des sessions
    - Profils utilisateurs et networking
    - Fonctionnalités essentielles pour mobile
    - Upload de fichiers et médias

    ## Authentification Mobile

    L'authentification se fait via token Bearer avec support des refresh tokens
    pour une expérience mobile optimale.

    ## Codes d'erreur

    L'API utilise les codes de statut HTTP standards avec des messages d'erreur
    optimisés pour l'affichage mobile.
  

This Dart package is automatically generated by the [OpenAPI Generator](https://openapi-generator.tech) project:

- API version: 1.0.0-mobile
- Generator version: 7.14.0
- Build package: org.openapitools.codegen.languages.DartDioClientCodegen

## Requirements

* Dart 2.15.0+ or Flutter 2.8.0+
* Dio 5.0.0+ (https://pub.dev/packages/dio)

## Installation & Usage

### pub.dev
To use the package from [pub.dev](https://pub.dev), please include the following in pubspec.yaml
```yaml
dependencies:
  proamlink_api: 1.0.0
```

### Github
If this Dart package is published to Github, please include the following in pubspec.yaml
```yaml
dependencies:
  proamlink_api:
    git:
      url: https://github.com/GIT_USER_ID/GIT_REPO_ID.git
      #ref: main
```

### Local development
To use the package from your local drive, please include the following in pubspec.yaml
```yaml
dependencies:
  proamlink_api:
    path: /path/to/proamlink_api
```

## Getting Started

Please follow the [installation procedure](#installation--usage) and then run the following:

```dart
import 'package:proamlink_api/proamlink_api.dart';


final api = ProamlinkApi().getSystmeApi();

try {
    final response = await api.systemHealth();
    print(response);
} catch on DioException (e) {
    print("Exception when calling SystmeApi->systemHealth: $e\n");
}

```

## Documentation for API Endpoints

All URIs are relative to *http://localhost:3000*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
[*SystmeApi*](doc/SystmeApi.md) | [**systemHealth**](doc/SystmeApi.md#systemhealth) | **GET** /api/rest/health | Vérification de l&#39;état de l&#39;API
[*TestApi*](doc/TestApi.md) | [**testComplexParams**](doc/TestApi.md#testcomplexparams) | **POST** /api/rest/test/complex | Complex parameter types test
[*TestApi*](doc/TestApi.md) | [**testErrorTest**](doc/TestApi.md#testerrortest) | **POST** /api/rest/test/error | Error handling test
[*TestApi*](doc/TestApi.md) | [**testProtectedDelete**](doc/TestApi.md#testprotecteddelete) | **DELETE** /api/rest/test/protected/{id} | Protected DELETE endpoint test
[*TestApi*](doc/TestApi.md) | [**testProtectedGet**](doc/TestApi.md#testprotectedget) | **GET** /api/rest/test/protected/{id} | Protected GET endpoint test
[*TestApi*](doc/TestApi.md) | [**testProtectedPut**](doc/TestApi.md#testprotectedput) | **PUT** /api/rest/test/protected/{id} | Protected PUT endpoint test
[*TestApi*](doc/TestApi.md) | [**testPublicGet**](doc/TestApi.md#testpublicget) | **GET** /api/rest/test/public | Public GET endpoint test
[*TestApi*](doc/TestApi.md) | [**testPublicPost**](doc/TestApi.md#testpublicpost) | **POST** /api/rest/test/public | Public POST endpoint test


## Documentation For Models

 - [ErrorBADREQUEST](doc/ErrorBADREQUEST.md)
 - [ErrorFORBIDDEN](doc/ErrorFORBIDDEN.md)
 - [ErrorINTERNALSERVERERROR](doc/ErrorINTERNALSERVERERROR.md)
 - [ErrorINTERNALSERVERERRORIssuesInner](doc/ErrorINTERNALSERVERERRORIssuesInner.md)
 - [ErrorNOTFOUND](doc/ErrorNOTFOUND.md)
 - [ErrorUNAUTHORIZED](doc/ErrorUNAUTHORIZED.md)
 - [SystemHealth200Response](doc/SystemHealth200Response.md)
 - [SystemHealth200ResponseServices](doc/SystemHealth200ResponseServices.md)
 - [TestComplexParamsRequest](doc/TestComplexParamsRequest.md)
 - [TestComplexParamsRequestObjectParam](doc/TestComplexParamsRequestObjectParam.md)
 - [TestErrorTestRequest](doc/TestErrorTestRequest.md)
 - [TestPublicGet200Response](doc/TestPublicGet200Response.md)
 - [TestPublicPostRequest](doc/TestPublicPostRequest.md)


## Documentation For Authorization


Authentication schemes defined for the API:
### bearerAuth

- **Type**: HTTP Bearer Token authentication (JWT)


## Author



