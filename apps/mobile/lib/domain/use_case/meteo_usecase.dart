import 'package:mobile/data/repository/meteo_repo_impl.dart';
import 'package:mobile/domain/models/temperature.dart';
import 'package:mobile/domain/models/weather.dart';
import 'package:mobile/domain/models/weather_condition.dart';
import 'package:mobile/domain/repository/meteo_repo.dart';

class MeteoUsecase {
  final MeteoRepository _meteoRepository;

  MeteoUsecase({MeteoRepository? meteoRepository})
      : _meteoRepository = meteoRepository ?? MeteoRepositoryImpl();

  Future<Weather> fetchCurrentWeather(String city) async {
    final location =
        await _meteoRepository.locationSearch({'name': city, 'count': '1'});

    final weatherData = await _meteoRepository.getWeather({
      'latitude': '${location.latitude}',
      'longitude': '${location.longitude}',
      'current_weather': 'true'
    });
    return Weather(
      condition: weatherData.weathercode.toInt().toCondition,
      lastUpdated: DateTime.now(),
      location: location.name,
      temperature: Temperature(value: weatherData.temperature),
    );
  }
}
