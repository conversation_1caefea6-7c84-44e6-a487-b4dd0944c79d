import 'package:mobile/domain/models/temperature.dart';
import 'package:mobile/domain/models/weather_condition.dart';

class Weather {
  final WeatherCondition condition;
  final DateTime lastUpdated;
  final String location;
  final Temperature temperature;

  Weather(
      {required this.condition,
      required this.lastUpdated,
      required this.location,
      required this.temperature});

  static final empty = Weather(
    condition: WeatherCondition.unknown,
    lastUpdated: DateTime(0),
    temperature: Temperature(value: 0),
    location: '--',
  );

  Weather copyWith({
    WeatherCondition? condition,
    DateTime? lastUpdated,
    String? location,
    Temperature? temperature,
  }) {
    return Weather(
      condition: condition ?? this.condition,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      location: location ?? this.location,
      temperature: temperature ?? this.temperature,
    );
  }
}
