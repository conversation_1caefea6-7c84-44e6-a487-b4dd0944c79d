import 'package:mobile/domain/models/temperature.dart';
import 'package:mobile/domain/models/weather.dart';
import 'package:mobile/domain/use_case/meteo_usecase.dart';
import 'package:stacked/stacked.dart';

class WeatherService with ListenableServiceMixin {
  final ReactiveValue<TemperatureUnits> _temperatureUnits =
      ReactiveValue<TemperatureUnits>(TemperatureUnits.celsius);

  final ReactiveValue<Weather> _weather = ReactiveValue<Weather>(Weather.empty);
  final ReactiveValue<WeatherStatus> _status =
      ReactiveValue<WeatherStatus>(WeatherStatus.initial);

  TemperatureUnits get temperatureUnits => _temperatureUnits.value;
  Weather get currentWeather => _weather.value;
  WeatherStatus get status => _status.value;
  final _useCase = MeteoUsecase();

  WeatherService() {
    listenToReactiveValues([_temperatureUnits, _weather, _status]);
  }

  Future<void> fetchWeather(String city) async {
    try {
      _status.value = WeatherStatus.loading;
      var weather = await _useCase.fetchCurrentWeather(city);

      final value = temperatureUnits.isFahrenheit
          ? weather.temperature.value.toFahrenheit()
          : weather.temperature.value;

      _weather.value = weather.copyWith(temperature: Temperature(value: value));

      _status.value = WeatherStatus.success;
    } on Exception {
      _status.value = WeatherStatus.failure;
      rethrow;
    }
  }

  Future<void> refreshWeather() async {
    try {
      final weather =
          await _useCase.fetchCurrentWeather(currentWeather.location);
      final units = temperatureUnits;
      final value = units.isFahrenheit
          ? weather.temperature.value.toFahrenheit()
          : weather.temperature.value;
      _temperatureUnits.value = units;
      _weather.value = weather.copyWith(temperature: Temperature(value: value));
      _status.value = WeatherStatus.success;
    } on Exception {
      //
    }
  }

  void toggleUnits() {
    final units = temperatureUnits.isFahrenheit
        ? TemperatureUnits.celsius
        : TemperatureUnits.fahrenheit;

    if (!status.isSuccess) {
      _temperatureUnits.value = units;
      return;
    }

    final weather = currentWeather;
    if (weather != Weather.empty) {
      final temperature = weather.temperature;
      final value = units.isCelsius
          ? temperature.value.toCelsius()
          : temperature.value.toFahrenheit();

      _temperatureUnits.value = units;
      _weather.value = weather.copyWith(temperature: Temperature(value: value));
    }
  }
}

extension TemperatureConversion on double {
  double toFahrenheit() => (this * 9 / 5) + 32;
  double toCelsius() => (this - 32) * 5 / 9;
}

enum WeatherStatus { initial, loading, success, failure }

extension WeatherStatusX on WeatherStatus {
  bool get isInitial => this == WeatherStatus.initial;
  bool get isLoading => this == WeatherStatus.loading;
  bool get isSuccess => this == WeatherStatus.success;
  bool get isFailure => this == WeatherStatus.failure;
}
