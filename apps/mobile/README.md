# Mobile App Architecture

This README outlines the architecture of a Flutter weather application built with the **Stacked** framework, using a layered **data**, **domain**, and **presentation** structure and the **MVVM** (Model-View-ViewModel) pattern. It is designed to be clear and accessible for beginners.

## Architecture Overview

The application is divided into three layers for modularity, maintainability, and testability:

1. **Data Layer**:

   - **Purpose**: Manages communication with external data sources (e.g., weather APIs) and converts raw data into Dart objects.
   - **Components**:
     - **Data Sources**: Handle API requests for weather and location data.
     - **Repositories**: Transform raw API responses into domain objects.
     - **Data Models**: Represent raw API data (e.g., temperature, weather codes).

2. **Domain Layer**:

   - **Purpose**: Contains business logic, core models, and use cases.
   - **Components**:
     - **Models**: Core objects like `Weather`, `Temperature`, and `WeatherCondition` enum.
     - **Repositories**: Abstract interfaces defining data access contracts.
     - **Use Cases**: Orchestrate business logic (e.g., fetching weather for a city). Created manually in `domain/use_cases/`.
     - **Services**: Manage global state and shared logic with `ListenableServiceMixin`.
     - **Extensions**: Utilities for data conversion (e.g., mapping weather codes to conditions).

3. **Presentation Layer**:
   - **Purpose**: Handles the user interface and presentation logic.
   - **Components**:
     - **Views**: Screens displaying data (e.g., weather information).
     - **ViewModels**: Connect views to services using `ReactiveViewModel`.
     - **Widgets**: Reusable UI components (e.g., buttons, weather icons).
     - **Common UI**: Shared styles and themes (e.g., dynamic theme based on weather).

## Folder Structure

- **`data/`**:

  - `data_source/`: API request logic.
  - `repository/`: Repository implementations.
  - Data model classes.

- **`domain/`**:

  - `models/`: Core models and enums.
  - `repository/`: Abstract repository interfaces.
  - `use_cases/`: Business logic coordination (manually created).
  - `services/`: State management and shared logic.

- **`presentation/`**:
  - `views/`: Application screens.
  - `viewmodels/`: Presentation logic.
  - `widgets/`: Reusable UI components.
  - `common_ui/`: Shared styles and themes.

## Data Flow

1. User interacts with a view (e.g., enters a city).
2. The view calls a ViewModel method.
3. The ViewModel interacts with a service.
4. The service uses a use case for business logic.
5. The use case calls a repository.
6. The repository fetches data via a data source.
7. Data is transformed and updates the UI reactively.

## Dependency Injection

- **Tool**: `get_it` manages dependencies.
- **Setup**: Services, repositories, and use cases are registered in `locator.dart` as singletons or factories.

## Stacked CLI Commands

Use **Stacked CLI** to generate boilerplate code. Install it with:

```bash
dart pub global activate stacked_cli
```

### Key Commands

1. **Create a View**:

   ```bash
   stacked create view weather
   ```

   - Creates `weather_view.dart` in `presentation/views` and `weather_viewmodel.dart` in `presentation/viewmodels`.

2. **Create a Service**:

   ```bash
   stacked create service weather
   ```

   - Creates `weather_service.dart` in `domain/services` with `ListenableServiceMixin`.

3. **Generate Routes**:

   ```bash
   stacked generate
   ```

   - Updates routing configuration for `StackedRouter`.

4. **Initialize a Project**:
   ```bash
   stacked create app weather_app
   ```
   - Sets up the initial project structure.

### Dependency for CLI

Add to `pubspec.yaml`:

```yaml
dev_dependencies:
  stacked_generator: ^1.3.0
```

## Getting Started

- **Setup**: Initialize `get_it` in `main.dart` with `setupLocator()` and configure Stacked services (`NavigationService`, etc.).
- **Reactivity**: Use `ViewModelBuilder` to connect views to ViewModels, which listen to services.
- **Theming**: Apply dynamic themes in `MaterialApp` based on weather conditions.
- **Use Cases**: Create use case files manually in `domain/use_cases/` to encapsulate business logic.

## Tips for Beginners

- **Learn MVVM**: Models hold data, views display UI, ViewModels connect them.
- **Keep Layers Separate**: Avoid mixing business logic in views or UI logic in services.
- **Use Stacked CLI**: Automate file creation for views and services.
- **Test Early**: Write unit tests for services and use cases using `mockito`.
- **Debugging**: Ensure services call `notifyListeners` and dependencies are registered in `locator.dart`.

## Resources

- [Stacked Documentation](https://stacked.filledstacks.com)
- [FilledStacks Tutorials](https://www.youtube.com/@FilledStacks)
- [Flutter Testing Guide](https://docs.flutter.dev/testing)
